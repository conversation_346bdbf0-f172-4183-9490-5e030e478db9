
const screenshot = document.getElementById("screenshot");
const componentMenu = document.getElementById("componentMenu");
const searchInput = document.getElementById("searchInput");

// Track labeled elements
const labeledElements = {
    'Navigation Bar': 0,
    'Button': 0,
    'Form Field': 0,
    'Card': 0,
    'Image': 0,
    'Icon': 0,
    'Text': 0,
    'Link': 0,
    'Dropdown': 0,
    'Checkbox': 0,
    'Radio Button': 0,
    'Toggle': 0,
    'Slider': 0,
    'Progress Bar': 0,
    'Table': 0,
    'Unknown Element': 0,
    'Non-UI Element': 0
};

// Current box visibility state
let boxVisibility = 'all'; // 'all', 'annotated', 'unannotated'

// Track total elements
let totalElements = 0;

// Keep track of currently selected element
let currentElement = null;

// Keep track of the current label type, selector, and label data for multi-labeling
let currentLabelType = null;
let currentSelector = null;
let currentLabelData = null;
let isProcessingLabel = false;

// Store a reference to the clicked element that won't be cleared by hideComponentMenu
let lastClickedElement = null;

// Fixed red color for all annotated elements
const ANNOTATION_COLOR = '#F44336';
let isVisible = false;

// Store elements loaded from annotation_object.json separately
let annotatedElementsData = [];
// Store elements loaded from sorted_elements.json
let sortedElementsData = [];

screenshot.onload = () => {
    console.log('Screenshot loaded, starting initialization...');
    // Set the wrapper width to match the image width
    const wrapper = document.getElementById("wrapper");
    console.log('Wrapper element found:', wrapper ? 'yes' : 'no');

    if (wrapper) {
        wrapper.style.width = "100%";
        document.body.style.width = "100%";
        console.log('Wrapper dimensions:', wrapper.offsetWidth, 'x', wrapper.offsetHeight);
    } else {
        console.error('Wrapper element not found during initialization!');
    }

    // Apply initial zoom level if it was set
    if (currentZoomLevel !== 100) {
        setZoomLevel(currentZoomLevel);
    }

    // Setup mouse and touch event listeners for zoom and pan
    setupZoomPanEventListeners();
    console.log('Zoom and pan event listeners initialized');

    // Setup component menu first
    setupComponentMenu();

    // Make sure the component menu is visible by default
    componentMenu.style.display = 'block';

    // Load annotated elements first, then sorted elements
    loadAnnotatedElements().then(() => {
        console.log('Annotated elements loaded, now loading sorted elements...');
        return loadSortedElements(); // Load sorted elements after annotated elements
    }).then(success => {
        if (success) {
            console.log('Sorted elements loaded successfully, combining and rendering...');
            loadElements(); // This function will now handle combining and rendering

            // Adjust bounding boxes after elements are loaded
            setTimeout(() => {
                adjustBoundingBoxes();
            }, 100);
        } else {
            console.error('Failed to load sorted elements.');
        }
    }).catch(error => {
        console.error('Error in element loading process:', error);
    });
};

// Parse URL parameters
const urlParams = new URLSearchParams(window.location.search);
const screenshotUrl = urlParams.get('screenshot');
const jsonUrl = urlParams.get('json');

// Set the screenshot source
if (screenshotUrl) {
    screenshot.src = screenshotUrl;
} else {
    screenshot.src = "/public/affindacom.png"; // Default fallback
}

function handleElementClick(e, element, isAnnotated) {

    try {
        e.stopPropagation();
        // if (typeof isSubmitted !== 'undefined' || isSubmitted) {
        if (recentlyDragged) {
            console.log('Click ignored - happened right after drag');
            return;
        }

        const selector = element.dataset.selector;
        applyHighlighting(element);

        currentElement = element;

        let currentLabels = [];
        try {
            currentLabels = JSON.parse(element.dataset.labels || '[]');
        } catch (e) {
            console.error('Error parsing labels:', e);
            if (element.dataset.elementType) {
                currentLabels = [element.dataset.elementType];
            }
        }

        updateCurrentLabelsSection(currentLabels);

        const saveButton = document.getElementById('saveLabelsBtn');
        saveButton.textContent = isAnnotated ? 'Update Label' : 'Save Label';

        document.querySelectorAll('.component-item input[type="checkbox"]').forEach(checkbox => {
            checkbox.checked = false;
        });
        selectedLabels = [];

        currentLabels.forEach(label => {
            const checkbox = document.querySelector(`.component-item input[data-type="${label}"]`);
            if (checkbox) {
                checkbox.checked = true;
                if (!selectedLabels.includes(label)) {
                    selectedLabels.push(label);
                }
            }
        });

        // Add any additional logic specific to annotated or unannotated elements here
        console.log('handleElementClick:', element, isAnnotated);
        // } else {
        //     console.log('isSubmittedInListener', isSubmitted);

        //     e.stopImmediatePropagation(); // Prevent document click from closing menu

        //     // Remove highlight from all boxes
        //     removeAllHighlights();
        //     console.log("CLASSLIST:", div.classList);
        //     // Highlight only this box
        //     div.classList.add('highlighted-box', 'highlighting-active-review');
        //     // div.classList.add('.);

        //     console.log("CLASSLIST:", div.classList);
        highlightAccordionItemFromBox(element);

        //     // Only proceed if the accordion component list is visible
        //     if (document.getElementById('accordionComponentList').style.display === 'block') {
        //         // Highlight the corresponding accordion item
        //         highlightAccordionItemFromBox(div);
        //         // Pan to the box
        //         panToElement(div);
        //     }
        // }
    } catch (error) {
        console.error('Error in handleElementClick:', error);
    }
}

// Function to load annotated elements from API
async function loadAnnotatedElements() {
    console.log('Loading annotated elements from API...');
    try {
        // Get the json_name from URL parameters
        const jsonName = jsonUrl ? jsonUrl.split('/').pop() : 'affindacom.json';
        console.log('Using json_name for API call:', jsonName);

        // Construct the API URL using the helper function
        const apiBaseUrl = getApiBaseUrl();
        const apiUrl = `${apiBaseUrl}/proxy/get-bounding-boxes?json_name=${encodeURIComponent(jsonName)}`;
        console.log('Fetching bounding boxes from:', apiUrl);

        const response = await fetch(apiUrl);
        if (!response.ok) {
            if (response.status === 404) {
                console.log('No bounding boxes found from API, starting with empty annotated elements.');
                annotatedElementsData = []; // Initialize as empty if not found
                return;
            }
            throw new Error(`Failed to load bounding boxes from API: ${response.status} ${response.statusText}`);
        }

        // Parse the response
        annotatedElementsData = await response.json();
        console.log('Bounding boxes loaded from API:', annotatedElementsData.length);

        // Transform the data if needed to match the expected format
        annotatedElementsData = annotatedElementsData.map(item => {
            // Create the transformed item without the labels property
            const transformedItem = {
                x: item.x,
                y: item.y,
                width: item.width,
                height: item.height,
                selector: item.selector || '',
                // Keep label as an array if it already is, otherwise convert to array
                label: Array.isArray(item.label) ? item.label : [item.label],
                class: item.class || '',
                id: item.id || '',
                tag: item.tag || '',
                // Preserve the isAccepted field if it exists
                isAccepted: item.isAccepted || null
            };

            return transformedItem;
        });

        console.log('Transformed bounding boxes:', annotatedElementsData);
    } catch (error) {
        console.error('Error loading bounding boxes from API:', error);
        annotatedElementsData = []; // Ensure it's empty in case of error
    }
}

// Function to load sorted elements from the provided JSON URL or default
async function loadSortedElements() {
    const elementsUrl = jsonUrl || "/affindacom.json";
    console.log('Loading sorted elements from:', elementsUrl);
    try {
        const response = await fetch(elementsUrl);
        if (!response.ok) {
            throw new Error(`Failed to load affindacom.json: ${response.status} ${response.statusText}`);
        }
        sortedElementsData = await response.json();
        console.log('Sorted elements loaded:', sortedElementsData.length);
        totalElements = sortedElementsData.length; // Update total elements count - we only render sorted elements
        return true; // Indicate successful loading
    } catch (error) {
        console.error('Error loading affindacom.json:', error);
        return false; // Indicate loading failure
    }
}


// Helper function to create a unique key for bounding boxes based on coordinates
function createBoundingBoxKey(element) {
    return `${element.x}-${element.y}-${element.width}-${element.height}`;
}

// Helper function to calculate area of a bounding box
function calculateArea(element) {
    // If area is already present in the element, use it
    if (element.area !== undefined) {
        return element.area;
    }
    // Otherwise calculate it from width and height
    return element.width * element.height;
}

// Helper function to filter, deduplicate, and sort bounding boxes by area
function getUniqueBoxes(sortedElements, annotatedElements) {
    console.log('Filtering for unique bounding boxes...');

    // Step 1: Create a map to store all unique boxes by their coordinate key
    const uniqueBoxesMap = new Map();

    // Step 2: First process all elements (both annotated and unannotated) to get a complete set
    // Add all sorted elements to the map first
    sortedElements.forEach(sortedEl => {
        const key = createBoundingBoxKey(sortedEl);
        uniqueBoxesMap.set(key, {
            element: sortedEl,
            isAnnotated: false,
            area: calculateArea(sortedEl)
        });
    });

    // Then, add or update with annotated elements (they have priority)
    annotatedElements.forEach(annotatedEl => {
        const key = createBoundingBoxKey(annotatedEl);
        uniqueBoxesMap.set(key, {
            element: annotatedEl,
            isAnnotated: true,
            area: calculateArea(annotatedEl)
        });
    });

    console.log(`Found ${uniqueBoxesMap.size} unique bounding boxes after deduplication`);

    // Step 3: Sort all boxes by area (largest to smallest)
    // This ensures consistent rendering order regardless of annotation status
    const sortedBoxes = new Map([...uniqueBoxesMap.entries()].sort((a, b) => {
        // Sort by area (largest to smallest)
        return b[1].area - a[1].area;
    }));

    console.log(`Sorted ${sortedBoxes.size} bounding boxes by area (largest to smallest)`);
    return sortedBoxes;
}

// Function to load and render elements from affindacom.json only
// and then update them with data from annotation_object.json
async function loadElements() {
    console.log('Starting to load elements...');
    const wrapper = document.getElementById("wrapper");
    const tooltip = document.getElementById("tooltip");
    const elementList = document.getElementById("elementList");

    // Create a Set to track rendered annotations
    const renderedAnnotations = new Set();

    // Clear any existing boxes and list items
    wrapper.querySelectorAll('.box').forEach(box => box.remove());
    // Check if elementList exists before trying to clear it
    if (elementList) {
        elementList.innerHTML = '';
    }

    // Get unique boxes with annotated elements having priority
    const uniqueBoxesMap = getUniqueBoxes(sortedElementsData, annotatedElementsData);

    console.log('Rendering unique bounding boxes...');

    // Render all unique boxes (now sorted by area)
    uniqueBoxesMap.forEach(({ element: el, isAnnotated, area }, key) => {
        const div = document.createElement("div");
        div.classList.add("box");

        // Store the annotation status in the dataset for debugging
        div.dataset.isAnnotated = isAnnotated.toString();

        // Apply z-index based on area - smaller boxes get higher z-index
        // This ensures inner boxes can be selected even when inside labeled outer boxes
        // Using a base of 1000 to ensure all boxes are above other elements but below UI components
        const zIndex = Math.max(1, 1000 - Math.floor(area / 1000));
        div.style.zIndex = zIndex.toString();

        if (isAnnotated) {
            div.classList.add("annotated-box"); // Apply different style for annotated elements
            console.log('Rendering annotated element:', el.selector, 'with area:', area, 'z-index:', zIndex);
        } else {
            console.log('Rendering element:', el.selector, 'with area:', area, 'z-index:', zIndex);
        }

        // Apply current visibility settings based on annotation status
        console.log(`Setting initial visibility for ${el.selector}: isAnnotated=${isAnnotated}, visibility=${boxVisibility}`);

        if (boxVisibility === 'all') {
            // Show all elements
            div.style.display = 'block';
        } else if (boxVisibility === 'annotated') {
            // Only show annotated elements
            div.style.display = isAnnotated ? 'block' : 'none';
        } else if (boxVisibility === 'unannotated') {
            // Only show unannotated elements
            div.style.display = !isAnnotated ? 'block' : 'none';
        }

        // Store original coordinates and dimensions in dataset for later adjustments
        div.dataset.originalX = el.x;
        div.dataset.originalY = el.y;
        div.dataset.originalWidth = el.width;
        div.dataset.originalHeight = el.height;
        div.dataset.area = area; // Store the area for debugging and future use

        // Set initial position and size
        div.style.left = `${el.x}px`;
        div.style.top = `${el.y}px`;
        div.style.width = `${el.width}px`;
        div.style.height = `${el.height}px`;
        div.dataset.selector = el.selector;
        div.dataset.labeled = el.label ? "true" : "false"; // Mark as labeled if label exists in data
        div.dataset.elementType = Array.isArray(el.label) ?
            el.label[0] :
            (el.label || "Unknown Element"); // Default type, or use loaded label

        // Set isAccepted property if it exists in the data
        if (el.isAccepted === "Yes") {
            div.dataset.isAccepted = "Yes";
            div.classList.add('accepted-box');
        }

        // Store color information if available
        if (el.color) {
            div.dataset.color = el.color;
        } else if (isAnnotated) {
            div.dataset.color = ANNOTATION_COLOR;
        }

        if (el.labels) {
            div.dataset.labels = JSON.stringify(el.labels); // Store all labels if available
        } else if (el.label) {
            // Handle different label formats
            if (Array.isArray(el.label)) {
                div.dataset.labels = JSON.stringify(el.label); // Already an array
            } else if (typeof el.label === 'string' && el.label.includes(',')) {
                // If it's a comma-separated string, split it
                const labelsArray = el.label.split(',').map(l => l.trim()).filter(l => l);
                div.dataset.labels = JSON.stringify(labelsArray);
            } else {
                div.dataset.labels = JSON.stringify([el.label]); // Store single label as array for consistency
            }
        } else {
            div.dataset.labels = JSON.stringify([]); // Initialize empty labels array
        }

        // Add mouseover event to highlight corresponding accordion item
        // div.addEventListener("mouseover", () => {
        //     // Only highlight if the accordion component list is visible
        //     if (document.getElementById('accordionComponentList').style.display === 'block') {
        //         highlightAccordionItemFromBox(div);
        //     }
        // });

        div.addEventListener("mouseout", () => {
            // Remove highlight from accordion item
            unhighlightAccordionItem();
        });

        console.log('IsSubmittedOnLoad', isSubmitted);
        div.addEventListener("click", (e) => handleElementClick(e, div, false));


        // For all elements (around line 415)

        wrapper.appendChild(div);

        // Also add to popup list if elementList exists
        if (elementList) {
            const listItem = document.createElement("li");
            listItem.textContent = el.selector;
            listItem.addEventListener("click", () => {
                // Apply highlighting and dimming
                applyHighlighting(el.selector);
            });
            elementList.appendChild(listItem);
        }

        // If the element is labeled or annotated, update its visual state immediately
        if (div.dataset.labeled === "true" || isAnnotated) {
            // For annotated elements, ensure they have the red color
            if (isAnnotated) {
                div.dataset.labeled = "true";
                if (!div.dataset.color) {
                    div.dataset.color = ANNOTATION_COLOR;
                }
            }
            updateElementVisualState(div);
        }
    });

    // Now render any annotated elements that weren't in sortedElementsData
    console.log('Checking for additional annotated elements...');
    let additionalCount = 0;

    annotatedElementsData.forEach(annotatedEl => {
        const key = `${annotatedEl.x}-${annotatedEl.y}-${annotatedEl.width}-${annotatedEl.height}`;

        // Skip if this annotation was already rendered
        if (renderedAnnotations.has(key)) {
            return;
        }

        additionalCount++;
        console.log('Rendering additional annotated element:', annotatedEl.selector);

        const div = document.createElement("div");
        div.classList.add("box", "annotated-box");

        // Store original coordinates and dimensions in dataset for later adjustments
        div.dataset.originalX = annotatedEl.x;
        div.dataset.originalY = annotatedEl.y;
        div.dataset.originalWidth = annotatedEl.width;
        div.dataset.originalHeight = annotatedEl.height;

        // Set initial position and size
        div.style.left = `${annotatedEl.x}px`;
        div.style.top = `${annotatedEl.y}px`;
        div.style.width = `${annotatedEl.width}px`;
        div.style.height = `${annotatedEl.height}px`;
        div.dataset.selector = annotatedEl.selector;
        div.dataset.labeled = "true"; // This is an annotated element
        div.dataset.elementType = Array.isArray(annotatedEl.label) ?
            annotatedEl.label[0] :
            (annotatedEl.label || "Unknown Element");

        // Store color information
        div.dataset.color = annotatedEl.color || ANNOTATION_COLOR;

        // Store labels in dataset.labels for UI purposes
        if (annotatedEl.label) {
            // Ensure label is treated as an array
            let labelsArray;

            if (Array.isArray(annotatedEl.label)) {
                // If it's already an array, use it
                labelsArray = annotatedEl.label;
            } else if (typeof annotatedEl.label === 'string' && annotatedEl.label.includes(',')) {
                // If it's a comma-separated string, split it
                labelsArray = annotatedEl.label.split(',').map(l => l.trim()).filter(l => l);
            } else {
                // Otherwise, wrap it in an array
                labelsArray = [annotatedEl.label];
            }

            div.dataset.labels = JSON.stringify(labelsArray);
        } else {
            div.dataset.labels = JSON.stringify([]);
        }

        // For additional annotated elements (in the loop)
        console.log("isSubmittedInONLOAD", isSubmitted)

        div.addEventListener("click", (e) => handleElementClick(e, div, true));


        // Add event listeners for annotated boxes
        // These are additional annotated elements, so isAnnotated should be true
        const isAnnotated = true;
        // console.log('isAnnotatedInside', isAnnotated);
        if (isAnnotated) {
            console.log('isSubmittedOutsideListener', isSubmitted);
            // Check if the Submit button has been clicked
            // Make sure isSubmitted is defined (it should be initialized as false at the top level)
            if (typeof isSubmitted !== 'undefined' && isSubmitted) {
                console.log('isSubmittedInListener', isSubmitted);


                // After Submit: Add click event to highlight accordion item and pan to the box
                div.addEventListener("click", (e) => {
                    console.log('isSubmittedInListener', isSubmitted);

                    e.stopImmediatePropagation(); // Prevent document click from closing menu

                    // Remove highlight from all boxes
                    removeAllHighlights();
                    console.log("CLASSLIST:", div.classList);
                    // Highlight only this box
                    div.classList.add('highlighted-box', 'highlighting-active');
                    // div.classList.add('.);

                    console.log("CLASSLIST:", div.classList);

                    // Only proceed if the accordion component list is visible
                    // if (document.getElementById('accordionComponentList').style.display === 'block') {
                        // Highlight the corresponding accordion item
                        // highlightAccordionItemFromBox(div);

                        // Pan to the box
                    //     panToElement(div);
                    // }
                });

                // Add a class to indicate the box is clickable
                div.classList.add('clickable-box');
            } else {
                // Before Submit: Add hover events to highlight accordion item
                div.addEventListener("click", () => {

                    // Only pan if the accordion component list is visible
                    if (document.getElementById('accordionComponentList').style.display === 'block') {
                        // Add pulsing animation to the box
                        // div.classList.add('panning-to-box');

                        // // Remove the animation class after it completes
                        // setTimeout(() => {
                        //     div.classList.remove('panning-to-box');
                        // }, 1500);

                        // Pan to the box
                        panToElement(div);
                    }
                });

                div.addEventListener("mouseover", () => {
                    // Only highlight if the accordion component list is visible
                    if (document.getElementById('accordionComponentList').style.display === 'block') {
                        highlightAccordionItemFromBox(div);

                        // Add a class to indicate the box is clickable
                        div.classList.add('clickable-box');
                    }
                });

                div.addEventListener("mouseout", () => {
                    // Remove highlight from accordion item
                    unhighlightAccordionItem();

                    // Remove the clickable indicator
                    div.classList.remove('clickable-box');
                });
            }
        }

        wrapper.appendChild(div);

        // Also add to popup list if elementList exists
        if (elementList) {
            const listItem = document.createElement("li");
            listItem.textContent = annotatedEl.selector;
            listItem.addEventListener("click", () => {
                // Apply highlighting and dimming
                applyHighlighting(annotatedEl.selector);
            });
            elementList.appendChild(listItem);
        }

        // Update visual state
        updateElementVisualState(div);
    });

    console.log(`Finished rendering UI elements. Added ${additionalCount} additional annotated elements.`);

    // If we're in review mode, show the left component menu
    if (isSubmitted) {
        console.log('Document is in review status, showing left component menu');
        updateComponentMenuForSubmit();
    }

    // Send initial label data to parent to update progress bar
    sendLabelDataToParent();
}


// Function to save JSON data to a file via API
async function saveJson(filename, data) {
    try {
        // Get the API base URL using the helper function
        const apiBaseUrl = getApiBaseUrl();

        const response = await fetch(`${apiBaseUrl}/save-annotation?file=${filename}`, {
            method: "POST",
            headers: { "Content-Type": "application/json" },
            body: JSON.stringify(data)
        });

        if (!response.ok) {
            const errorData = await response.json();
            throw new Error(`API error: ${errorData.error || response.statusText}`);
        }

        console.log(`Data saved to ${filename} successfully via API.`);
        return true;
    } catch (error) {
        console.error(`Error saving data to ${filename}:`, error);
        alert(`Error saving data to ${filename}. Check console for details.`);
        return false;
    }
}

// Function to apply label to an element without saving to server
function applyLabelToElement(element, elementType) {
    console.log('Applying label:', elementType, 'to element:', element.dataset.selector);

    // Initialize labels array if it doesn't exist
    if (!element.dataset.labels) {
        element.dataset.labels = JSON.stringify([]);
    }

    // Get current labels
    let currentLabels = JSON.parse(element.dataset.labels);

    // Check if this label already exists
    if (!currentLabels.includes(elementType)) {
        // Add the new label
        currentLabels.push(elementType);
        element.dataset.labels = JSON.stringify(currentLabels);

        // Increment count for this element type
        labeledElements[elementType] = (labeledElements[elementType] || 0) + 1;
    }

    // Set labeled status
    element.dataset.labeled = "true";
    // Keep the last label as the primary one for backward compatibility
    element.dataset.elementType = elementType;

    // Create label data object using original coordinates from dataset
    const labelData = {
        x: parseInt(element.dataset.originalX || element.style.left),
        y: parseInt(element.dataset.originalY || element.style.top),
        width: parseInt(element.dataset.originalWidth || element.style.width),
        height: parseInt(element.dataset.originalHeight || element.style.height),
        selector: element.dataset.selector,
        // Removed labels property since it's redundant
        label: currentLabels, // Store all labels as an array in the label property

        timestamp: new Date().toISOString()
    };

    // Log the coordinates being used
    console.log('Using coordinates for label:', labelData.x, labelData.y, labelData.width, labelData.height);

    // Update visual indication immediately
    updateElementVisualState(element);

    // If we're in review mode (left menu is visible), update the left accordion menu in real-time
    if (isSubmitted && document.getElementById('leftComponentMenu').style.display === 'flex') {
        // Update the annotatedElementsData with the new label
        const elementKey = createBoundingBoxKey(labelData);
        const existingIndex = annotatedElementsData.findIndex(el => createBoundingBoxKey(el) === elementKey);

        if (existingIndex !== -1) {
            // Update the existing element with the new labels
            annotatedElementsData[existingIndex].label = currentLabels;
        } else {
            // Add the new element to annotatedElementsData
            annotatedElementsData.push(labelData);
        }

        // Re-render the left accordion menu to reflect the updated labels
        renderLeftAccordionComponentList();
    }

    // Send updated label data to parent to update progress bar
    sendLabelDataToParent();

    return labelData;
}

// Function to apply label to an element and handle multi-label popup if needed
async function applyLabel(element, elementType, skipPopup = false) {
    console.log('Processing label:', elementType, 'for element:', element.dataset.selector);

    // Apply the label to the element in the UI
    const labelData = applyLabelToElement(element, elementType);

    // Save the label data for the current element
    await saveElementLabel(labelData);

    // If skipPopup is true, just return without showing popup
    if (skipPopup) {
        return labelData;
    }

    // Check if there are other elements with the same selector that are not yet labeled
    const selector = element.dataset.selector;

    // Get all elements with the same selector
    const sameClassElements = Array.from(document.querySelectorAll(`.box[data-selector="${selector}"]`));
    console.log('Found elements with same selector:', sameClassElements.length);

    // Filter out the current element and already labeled elements
    const unlabeledSameClassElements = sameClassElements.filter(el => {
        // Check if this is not the current element and it's not labeled
        const isNotCurrentElement = el !== element;
        const isNotLabeled = el.dataset.labeled !== "true";
        return isNotCurrentElement && isNotLabeled;
    });

    console.log('Found unlabeled same class elements:', unlabeledSameClassElements.length);

    if (unlabeledSameClassElements.length > 0) {
        // Store current label type, selector, and the current element's label data for use in the popup handler
        currentLabelType = elementType;
        currentSelector = selector;
        currentLabelData = labelData;

        // Show the multi-label popup
        showMultiLabelPopup();
    }
    // No need to reload elements as visual state is already updated by applyLabelToElement

    // Send updated data to parent
    sendLabelDataToParent();

    return labelData;
}

// Function to save a label to the server
async function saveElementLabel(labelData) {
    try {
        console.log('Saving label data with coordinates:', labelData.x, labelData.y, labelData.width, labelData.height);

        // Create a key for the current label data
        const labelKey = createBoundingBoxKey(labelData);

        // Add to annotated data if not already there
        const alreadyExists = annotatedElementsData.some(el => createBoundingBoxKey(el) === labelKey);

        if (!alreadyExists) {
            annotatedElementsData.push(labelData);
            console.log('Added new annotation to annotatedElementsData');
        } else {
            // Update the existing annotation with new labels
            const existingIndex = annotatedElementsData.findIndex(el => createBoundingBoxKey(el) === labelKey);

            if (existingIndex !== -1) {
                // Keep the existing coordinates but update other properties
                const existingCoords = {
                    x: annotatedElementsData[existingIndex].x,
                    y: annotatedElementsData[existingIndex].y,
                    width: annotatedElementsData[existingIndex].width,
                    height: annotatedElementsData[existingIndex].height
                };

                annotatedElementsData[existingIndex] = {
                    ...labelData,
                    ...existingCoords
                };
                console.log('Updated existing annotation in annotatedElementsData with coordinates:',
                    existingCoords.x, existingCoords.y, existingCoords.width, existingCoords.height);
            }
        }

        // Remove from sorted data if it exists there (using the same key-based approach)
        sortedElementsData = sortedElementsData.filter(el => {
            // If the coordinates match but it's not the same selector, we still want to keep it unique by coordinates
            return createBoundingBoxKey(el) !== labelKey;
        });

        // Save the updated annotated data to annotation_object.json
        const saveSuccess = await saveJson('annotation_object.json', annotatedElementsData);

        if (saveSuccess) {
            console.log('Annotation saved to annotation_object.json successfully.');
            return true;
        } else {
            throw new Error('Failed to save annotation to annotation_object.json');
        }
    } catch (error) {
        console.error('Error saving annotation:', error);
        alert('Error saving annotation. Check console for details.');
        return false;
    }
}

// Function to remove a label from an element
async function removeLabelFromElement(element, labelToRemove) {
    console.log('Removing label:', labelToRemove, 'from element:', element.dataset.selector);

    // Get the current labels
    let currentLabels = [];
    try {
        currentLabels = JSON.parse(element.dataset.labels || '[]');
    } catch (e) {
        console.error('Error parsing labels:', e);
        return false;
    }

    // Check if the label exists
    if (!currentLabels.includes(labelToRemove)) {
        console.warn('Label not found in element labels');
        return false;
    }

    // Remove the label from the array
    const labelIndex = currentLabels.indexOf(labelToRemove);
    if (labelIndex !== -1) {
        currentLabels.splice(labelIndex, 1);
    }

    // Decrement count for this element type
    if (labeledElements[labelToRemove]) {
        labeledElements[labelToRemove] = Math.max(0, labeledElements[labelToRemove] - 1);
    }

    // Get the element coordinates
    const selector = element.dataset.selector;
    const x = parseInt(element.dataset.originalX || element.style.left);
    const y = parseInt(element.dataset.originalY || element.style.top);
    const width = parseInt(element.dataset.originalWidth || element.style.width);
    const height = parseInt(element.dataset.originalHeight || element.style.height);

    console.log('Element coordinates for label removal:', x, y, width, height);

    // Create a key for the element
    const elementKey = `${x}-${y}-${width}-${height}`;

    try {
        // Parse the selector to extract tag, class, and id information
        const selectorInfo = parseSelector(selector);
        const tag = selectorInfo.tag;
        const elementClass = selectorInfo.class;
        const id = selectorInfo.id;

        // Find the existing item in annotatedElementsData to get all properties
        const existingItem = annotatedElementsData.find(item => createBoundingBoxKey(item) === elementKey);

        // Prepare data for the API call using the append API
        let apiData = [];

        if (currentLabels.length === 0) {
            // If no labels left, mark as unlabeled and remove from annotatedElementsData
            element.dataset.labeled = 'false';
            element.dataset.elementType = '';
            element.dataset.labels = JSON.stringify([]);

            console.log('No labels left, removing element from annotatedElementsData');

            // Filter out this element from annotatedElementsData using the key
            annotatedElementsData = annotatedElementsData.filter(el => createBoundingBoxKey(el) !== elementKey);

            // Reset the element's visual state
            element.style.borderWidth = '1px';
            element.style.borderStyle = 'solid';
            element.style.borderColor = '#ccc';
            element.style.backgroundColor = 'transparent';
            element.classList.remove('labeled-ui', 'labeled-unsure', 'labeled-not-ui');

            // Remove the labels container
            const labelsContainer = element.querySelector('.labels-container');
            if (labelsContainer) {
                labelsContainer.remove();
            }

            // For completely removing the label, we need to send an empty label array
            // but include all other properties that the backend expects
            apiData = [
                {
                    selector: selector,
                    label: [""],  // Use [""] instead of [] to avoid parsing issues in the backend
                    x: x,
                    y: y,
                    width: width,
                    height: height,
                    tag: tag,
                    class: elementClass,
                    id: id,
                    timestamp: new Date().toISOString()
                }
            ];
        } else {
            // If labels remain, update the dataset and visual state
            element.dataset.labels = JSON.stringify(currentLabels);
            element.dataset.elementType = currentLabels[currentLabels.length - 1]; // Use the last label as primary

            // Update the element's visual state
            updateElementVisualState(element);

            // Find the element in annotatedElementsData using the key
            const annotatedIndex = annotatedElementsData.findIndex(el => createBoundingBoxKey(el) === elementKey);

            if (annotatedIndex !== -1) {
                // Update only the label property with all labels
                annotatedElementsData[annotatedIndex].label = currentLabels;
                // Remove the labels property if it exists
                if (annotatedElementsData[annotatedIndex].hasOwnProperty('labels')) {
                    delete annotatedElementsData[annotatedIndex].labels;
                }
            }

            // Create data object with updated labels, including all properties from existing item
            const dataObj = {
                selector: selector,
                label: currentLabels,
                x: x,
                y: y,
                width: width,
                height: height,
                tag: tag,
                class: elementClass,
                id: id,
                timestamp: new Date().toISOString()
            };

            // If we have an existing item, copy any additional properties
            if (existingItem) {
                // Copy any other properties that might be needed by the backend
                for (const key in existingItem) {
                    if (!dataObj.hasOwnProperty(key) && key !== 'label' && key !== 'labels') {
                        dataObj[key] = existingItem[key];
                    }
                }
            }

            apiData = [dataObj];
        }

        // Get the filename from the URL
        const urlParams = new URLSearchParams(window.location.search);
        const jsonName = urlParams.get('json') || 'annotation_object.json';

        // Call the append API with the updated labels
        console.log('Calling appendLabels with updated labels:', apiData);
        const success = await appendLabels(jsonName, apiData);

        if (!success) {
            throw new Error('Failed to update labels via append API');
        }

        // Send updated data to parent
        sendLabelDataToParent();

        // If we're in review mode (left menu is visible), update the left accordion menu in real-time
        if (isSubmitted && document.getElementById('leftComponentMenu').style.display === 'flex') {
            // Re-render the left accordion menu to reflect the updated labels
            renderLeftAccordionComponentList();
        }

        console.log('Label removed successfully');
        return true;
    } catch (error) {
        console.error('Error removing label:', error);
        alert('Error removing label. Check console for details.');
        return false;
    }
}

// Helper function to parse selector and extract tag, class, and id
function parseSelector(selector) {
    const result = {
        tag: '',
        class: '',
        id: ''
    };

    // Extract tag
    const tagMatch = selector.match(/^([a-zA-Z][a-zA-Z0-9]*)/);
    if (tagMatch) {
        result.tag = tagMatch[1];
    }

    // Extract classes
    const classMatches = selector.match(/\.[a-zA-Z][a-zA-Z0-9_-]*/g);
    if (classMatches) {
        result.class = classMatches.map(c => c.substring(1)).join(' ');
    }

    // Extract id
    const idMatch = selector.match(/#([a-zA-Z][a-zA-Z0-9_-]*)/);
    if (idMatch) {
        result.id = idMatch[1];
    }

    return result;
}

// Function to apply the same label to all elements with the same selector
async function applyLabelToAllSameClass(selector, elementType) {
    console.log('Applying label to all elements with selector:', selector);

    // Get all elements with the same selector
    const elements = Array.from(document.querySelectorAll(`.box[data-selector="${selector}"]`));
    console.log('Found elements with same selector:', elements.length);

    // Filter to get only unlabeled elements
    const unlabeledElements = elements.filter(el => el.dataset.labeled !== "true");
    console.log('Found unlabeled elements:', unlabeledElements.length);

    // If there are no unlabeled elements, just return
    if (unlabeledElements.length === 0) {
        console.log('No unlabeled elements found with selector:', selector);
        return;
    }

    // Apply label to each unlabeled element
    for (const element of unlabeledElements) {
        console.log('Applying label to element:', element.dataset.selector);
        // Apply the label to the element in the UI
        const labelData = applyLabelToElement(element, elementType);
        // Save the label to the server
        await saveElementLabel(labelData);
        // Note: Visual state is already updated by applyLabelToElement
    }

    // Send updated label data to parent to update progress bar
    sendLabelDataToParent();

    // No need to reload elements as visual state is already updated
    // This prevents the UI from flickering and ensures immediate color updates
    // loadElements(); // Removed to prevent resetting visual state
}

// Function to show the multi-label popup next to the selected bounding box
function showMultiLabelPopup() {
    console.log('Showing multi-label popup');
    const popup = document.getElementById('multiLabelPopup');
    const overlay = document.getElementById('popupOverlay');
    const arrow = popup.querySelector('.popup-arrow');
    const popupText = popup.querySelector('p');

    // Check if the current element is annotated
    const isAnnotated = currentElement && (currentElement.dataset.isAnnotated === 'true' || currentElement.classList.contains('annotated-box'));

    // Update popup text based on whether the element is annotated
    if (isAnnotated) {
        popupText.textContent = 'At this time now you want to Update for All highlighted elements';
    } else {
        popupText.textContent = 'Do you want to apply these labels to all bounding boxes with the same selector?';
    }

    // Make sure component menu is still visible
    const componentMenu = document.getElementById('componentMenu');
    componentMenu.style.display = 'block';

    // Reset arrow classes
    arrow.className = 'popup-arrow';

    // Show the overlay
    overlay.style.display = 'block';
    overlay.style.zIndex = '2999';

    // Position the popup next to the currently selected bounding box
    if (currentElement) {
        // Get the bounding box position and dimensions
        const boxRect = currentElement.getBoundingClientRect();
        console.log('Box rect for popup positioning:', boxRect);

        // Make the popup visible to calculate its dimensions
        popup.style.display = 'block';
        popup.style.zIndex = '3000';

        // Get the popup dimensions
        const popupRect = popup.getBoundingClientRect();

        // Get viewport dimensions
        const viewportWidth = window.innerWidth;
        const viewportHeight = window.innerHeight;

        // Calculate the best position for the popup
        let popupLeft, popupTop;
        let arrowPosition = '';

        // Try to position to the right of the box
        if (boxRect.right + popupRect.width + 20 < viewportWidth) {
            // Position to the right
            popupLeft = boxRect.right + 20;
            popupTop = boxRect.top + (boxRect.height / 2) - (popupRect.height / 2);
            arrowPosition = 'left';
        }
        // Try to position to the left of the box
        else if (boxRect.left - popupRect.width - 20 > 0) {
            // Position to the left
            popupLeft = boxRect.left - popupRect.width - 20;
            popupTop = boxRect.top + (boxRect.height / 2) - (popupRect.height / 2);
            arrowPosition = 'right';
        }
        // Try to position below the box
        else if (boxRect.bottom + popupRect.height + 20 < viewportHeight) {
            // Position below
            popupLeft = boxRect.left + (boxRect.width / 2) - (popupRect.width / 2);
            popupTop = boxRect.bottom + 20;
            arrowPosition = 'top';
        }
        // Try to position above the box
        else if (boxRect.top - popupRect.height - 20 > 0) {
            // Position above
            popupLeft = boxRect.left + (boxRect.width / 2) - (popupRect.width / 2);
            popupTop = boxRect.top - popupRect.height - 20;
            arrowPosition = 'bottom';
        }
        // Fallback to center of the screen if no good position is found
        else {
            popupLeft = (viewportWidth - popupRect.width) / 2;
            popupTop = (viewportHeight - popupRect.height) / 2;
            arrowPosition = ''; // No arrow in center position
        }

        // Ensure the popup stays within the viewport
        popupLeft = Math.max(10, Math.min(viewportWidth - popupRect.width - 10, popupLeft));
        popupTop = Math.max(10, Math.min(viewportHeight - popupRect.height - 10, popupTop));

        // Set the popup position
        popup.style.transform = 'none'; // Remove the default center positioning
        popup.style.left = `${popupLeft}px`;
        popup.style.top = `${popupTop}px`;

        // Position the arrow
        if (arrowPosition) {
            arrow.classList.add(`arrow-${arrowPosition}`);
        }
    } else {
        // Fallback to center positioning if no current element
        popup.style.left = '50%';
        popup.style.top = '50%';
        popup.style.transform = 'translate(-50%, -50%)';
        popup.style.display = 'block';
        popup.style.zIndex = '3000';
    }
}

// Function to hide the multi-label popup
function hideMultiLabelPopup() {
    const popup = document.getElementById('multiLabelPopup');
    const overlay = document.getElementById('popupOverlay');

    // Hide the overlay and popup
    overlay.style.display = 'none';
    popup.style.display = 'none';

    // Make sure component menu is still visible
    const componentMenu = document.getElementById('componentMenu');
    componentMenu.style.display = 'block';
}

// Update visual state of element based on its labels - remains mostly the same
function updateElementVisualState(element) {
    // Remove any existing label classes
    element.classList.remove('labeled-ui', 'labeled-unsure', 'labeled-not-ui');

    // Add appropriate class based on the primary label (for backward compatibility)
    if (element.dataset.elementType === 'Unknown Element') {
        element.classList.add('labeled-unsure');
    } else if (element.dataset.elementType === 'Non-UI Element') {
        element.classList.add('labeled-not-ui');
    } else {
        element.classList.add('labeled-ui');
    }

    // Add annotated-box class if it's not already there
    if (!element.classList.contains('annotated-box')) {
        element.classList.add('annotated-box');
    }

    // Add accepted-box class if the element is accepted
    if (element.dataset.isAccepted === "Yes") {
        element.classList.add('accepted-box');
    } else {
        element.classList.remove('accepted-box');
    }

    // Apply current visibility settings based on annotation status
    const isAnnotated = element.classList.contains('annotated-box');

    // Log for debugging
    console.log(`Updating element ${element.dataset.selector} visual state: isAnnotated=${isAnnotated}, visibility=${boxVisibility}`);

    if (boxVisibility === 'all') {
        // Show all elements
        element.style.display = 'block';
    } else if (boxVisibility === 'annotated') {
        // Only show annotated elements
        element.style.display = isAnnotated ? 'block' : 'none';
    } else if (boxVisibility === 'unannotated') {
        // Only show unannotated elements
        element.style.display = !isAnnotated ? 'block' : 'none';
    }

    // Get all labels
    let labels = [];
    if (element.dataset.labels) {
        try {
            labels = JSON.parse(element.dataset.labels);
        } catch (e) {
            console.error('Error parsing labels:', e);
            labels = [element.dataset.elementType]; // Fallback to primary label
        }
    } else if (element.dataset.elementType) {
        // Fallback to primary label if no labels array
        labels = [element.dataset.elementType];
    }

    // Remove any existing labels container
    const existingContainer = element.querySelector('.labels-container');
    if (existingContainer) {
        existingContainer.remove();
    }

    // Create a container for multiple labels
    const labelsContainer = document.createElement('div');
    labelsContainer.className = 'labels-container';

    // No scaling for labels

    element.appendChild(labelsContainer);

    // Add each label as a separate element
    labels.forEach(label => {
        // Skip empty labels or handle comma-separated labels
        if (!label) return;

        // If the label contains commas, split it into multiple labels
        if (typeof label === 'string' && label.includes(',')) {
            const splitLabels = label.split(',').map(l => l.trim()).filter(l => l);

            // Add each split label separately
            splitLabels.forEach(splitLabel => {
                const labelElement = document.createElement('div');
                labelElement.className = 'multi-label';

                // Create a span for the label text
                const labelText = document.createElement('span');
                labelText.textContent = splitLabel;
                labelElement.appendChild(labelText);

                // Create the cross icon for removing the label
                const removeIcon = document.createElement('span');
                removeIcon.className = 'label-remove';
                removeIcon.textContent = '×';
                removeIcon.title = 'Remove this label';

                // Add click event to remove the label
                removeIcon.addEventListener('click', async (e) => {
                    e.stopPropagation(); // Prevent the box click event from firing
                    await removeLabelFromElement(element, splitLabel);

                    // Update the current labels section in the right panel if this is the current element
                    if (element === currentElement) {
                        try {
                            const updatedLabels = JSON.parse(element.dataset.labels || '[]');
                            updateCurrentLabelsSection(updatedLabels);

                            // Also update the checkbox state
                            const checkbox = document.querySelector(`.component-item input[data-type="${splitLabel}"]`);
                            if (checkbox) {
                                checkbox.checked = false;

                                // Remove from selected labels array
                                const index = selectedLabels.indexOf(splitLabel);
                                if (index !== -1) {
                                    selectedLabels.splice(index, 1);
                                }
                            }
                        } catch (e) {
                            console.error('Error updating labels after removal:', e);
                        }
                    }
                });

                labelElement.appendChild(removeIcon);
                labelsContainer.appendChild(labelElement);
            });
        } else {
            // Regular single label
            const labelElement = document.createElement('div');
            labelElement.className = 'multi-label';

            // Create a span for the label text
            const labelText = document.createElement('span');
            labelText.textContent = label;
            labelElement.appendChild(labelText);

            // Create the cross icon for removing the label
            const removeIcon = document.createElement('span');
            removeIcon.className = 'label-remove';
            removeIcon.textContent = '×';
            removeIcon.title = 'Remove this label';

            // Add click event to remove the label
            removeIcon.addEventListener('click', async (e) => {
                e.stopPropagation(); // Prevent the box click event from firing
                await removeLabelFromElement(element, label);

                // Update the current labels section in the right panel if this is the current element
                if (element === currentElement) {
                    try {
                        const updatedLabels = JSON.parse(element.dataset.labels || '[]');
                        updateCurrentLabelsSection(updatedLabels);

                        // Also update the checkbox state
                        const checkbox = document.querySelector(`.component-item input[data-type="${label}"]`);
                        if (checkbox) {
                            checkbox.checked = false;

                            // Remove from selected labels array
                            const index = selectedLabels.indexOf(label);
                            if (index !== -1) {
                                selectedLabels.splice(index, 1);
                            }
                        }
                    } catch (e) {
                        console.error('Error updating labels after removal:', e);
                    }
                }
            });

            labelElement.appendChild(removeIcon);
            labelsContainer.appendChild(labelElement);
        }
    });

    // Make sure the labels container is visible and set to flex-direction: column
    // labelsContainer.style.display = 'flex';
    // labelsContainer.style.flexDirection = 'column';
    labelsContainer.style.zIndex = '1000'; // Ensure it's on top

    // Add a data attribute to the element to mark it as labeled
    // This will help with persistence after refresh
    element.setAttribute('data-is-labeled', 'true');

    // Add a border to make the labeled element more visible
    element.style.borderWidth = '3px';
    element.style.borderStyle = 'solid';

    // Set border color based on element type
    if (element.dataset.elementType === 'Unknown Element') {
        element.style.borderColor = 'orange';
        element.style.backgroundColor = 'rgba(255, 165, 0, 0.2)';
    } else if (element.dataset.elementType === 'Non-UI Element') {
        element.style.borderColor = '#9E9E9E'; // Gray
        element.style.backgroundColor = 'rgba(158, 158, 158, 0.2)';
    } else {
        // For all annotated UI elements, use the fixed red color
        element.style.borderColor = ANNOTATION_COLOR;
        element.style.backgroundColor = 'rgba(244, 67, 54, 0.2)'; // Light red background
    }

    // For backward compatibility, also update the single label tag if it exists
    let labelTag = element.querySelector('.label-tag');
    if (labelTag) {
        labelTag.style.display = 'none'; // Hide the old-style label
    }
}

// Show component menu with updated functionality for annotated boxes
function showComponentMenu() {
    // Always position the menu at the right side of the screen
    componentMenu.style.right = '0';
    componentMenu.style.top = '0';
    componentMenu.style.display = 'block';

    // Clear search input
    searchInput.value = '';

    // Reset all checkboxes and selected labels array
    selectedLabels = [];
    document.querySelectorAll('.component-item input[type="checkbox"]').forEach(checkbox => {
        checkbox.checked = false;
    });

    // Check if the current element is annotated
    const isAnnotated = currentElement && (currentElement.dataset.isAnnotated === 'true' || currentElement.classList.contains('annotated-box'));

    // Update the save button text based on whether the element is annotated
    const saveButton = document.getElementById('saveLabelsBtn');
    const unsureButton = document.getElementById('markAsUnsure');
    if (isAnnotated) {
        saveButton.textContent = 'Update Label';
        unsureButton.textContent = 'Now I have an Doubt';
        console.log('Element is annotated, changing button texts');

        // Get the current labels from the element
        let currentLabels = [];
        try {
            currentLabels = JSON.parse(currentElement.dataset.labels || '[]');
        } catch (e) {
            console.error('Error parsing labels:', e);
            // If there's an error, try to get the primary label
            if (currentElement.dataset.elementType) {
                currentLabels = [currentElement.dataset.elementType];
            }
        }

        console.log('Current labels for annotated element:', currentLabels);

        // Update the current labels section
        updateCurrentLabelsSection(currentLabels);

        // Pre-select the checkboxes for the current labels
        currentLabels.forEach(label => {
            // Find the checkbox with the matching data-type
            const checkbox = document.querySelector(`.component-item input[data-type="${label}"]`);
            if (checkbox) {
                checkbox.checked = true;
                // Add to selected labels array
                if (!selectedLabels.includes(label)) {
                    selectedLabels.push(label);
                }
            }
        });
    } else {
        saveButton.textContent = 'Save Labels';
        unsureButton.textContent = 'Mark as "Unsure"';
        // Hide the current labels section if no labels
        document.getElementById('currentLabelsSection').style.display = 'none';
    }

    // Reset component visibility
    document.querySelectorAll('.component-item').forEach(item => {
        item.style.display = 'block';
    });

    // Focus search input
    searchInput.focus();
}

// Function to update the current labels section in the component menu
function updateCurrentLabelsSection(labels) {
    const currentLabelsSection = document.getElementById('currentLabelsSection');
    const currentLabelsList = document.getElementById('currentLabelsList');

    // Clear the current labels list
    currentLabelsList.innerHTML = '';

    // If there are no labels, hide the section
    if (!labels || labels.length === 0) {
        currentLabelsSection.style.display = 'none';
        return;
    }

    // Show the section
    currentLabelsSection.style.display = 'block';

    // Add each label to the list
    labels.forEach(label => {
        if (!label) return; // Skip empty labels

        const labelItem = document.createElement('div');
        labelItem.className = 'current-label-item';

        // Create a span for the label text
        const labelText = document.createElement('span');
        labelText.textContent = label;
        labelItem.appendChild(labelText);

        // Create the cross icon for removing the label
        const removeIcon = document.createElement('span');
        removeIcon.className = 'current-label-remove';
        removeIcon.textContent = '×';
        removeIcon.title = 'Remove this label';

        // Add click event to remove the label
        removeIcon.addEventListener('click', async (e) => {
            e.stopPropagation(); // Prevent the box click event from firing
            if (currentElement) {
                await removeLabelFromElement(currentElement, label);

                // Update the current labels section after removal
                try {
                    const updatedLabels = JSON.parse(currentElement.dataset.labels || '[]');
                    updateCurrentLabelsSection(updatedLabels);

                    // Also update the checkbox state
                    const checkbox = document.querySelector(`.component-item input[data-type="${label}"]`);
                    if (checkbox) {
                        checkbox.checked = false;

                        // Remove from selected labels array
                        const index = selectedLabels.indexOf(label);
                        if (index !== -1) {
                            selectedLabels.splice(index, 1);
                        }
                    }
                } catch (e) {
                    console.error('Error updating labels after removal:', e);
                }
            }
        });

        labelItem.appendChild(removeIcon);
        currentLabelsList.appendChild(labelItem);
    });
}

// Using fixed colors, no need for color conversion functions

// Array to store selected labels
let selectedLabels = [];

// Function to update the annotated elements data in memory and refresh the UI
function updateAnnotatedElementsData(newData) {
    console.log('Updating annotated elements data with new data:', newData);

    // For each item in the new data
    for (const item of newData) {
        const selector = item.selector;
        const x = item.x;
        const y = item.y;
        const width = item.width;
        const height = item.height;

        // Create a key for the current item
        const itemKey = createBoundingBoxKey(item);

        // Find if this item already exists in annotatedElementsData
        const existingIndex = annotatedElementsData.findIndex(existing => createBoundingBoxKey(existing) === itemKey);

        if (existingIndex !== -1) {
            // Update existing item
            // Preserve the BulkAnnotated field if it exists in the item
            const bulkAnnotated = item.BulkAnnotated || annotatedElementsData[existingIndex].BulkAnnotated;
            annotatedElementsData[existingIndex] = {
                ...annotatedElementsData[existingIndex],
                ...item,
                timestamp: new Date().toISOString(),
                // Keep the BulkAnnotated field if it exists
                ...(bulkAnnotated ? { BulkAnnotated: bulkAnnotated } : {})
            };
            console.log('Updated existing item in annotatedElementsData:', selector, x, y);
        } else {
            // Add new item
            annotatedElementsData.push({
                ...item,
                timestamp: new Date().toISOString()
            });
            console.log('Added new item to annotatedElementsData:', selector, x, y);

            // Remove any duplicate from sortedElementsData based on coordinates
            sortedElementsData = sortedElementsData.filter(el => createBoundingBoxKey(el) !== itemKey);
        }

        // Update the UI for this element
        // First try to find by exact coordinates
        const boxElements = document.querySelectorAll('.box');
        let matchingElement = null;

        // Find the element with matching coordinates
        for (const element of boxElements) {
            const elX = parseInt(element.dataset.originalX || element.style.left);
            const elY = parseInt(element.dataset.originalY || element.style.top);
            const elWidth = parseInt(element.dataset.originalWidth || element.style.width);
            const elHeight = parseInt(element.dataset.originalHeight || element.style.height);

            if (elX === x && elY === y && elWidth === width && elHeight === height) {
                matchingElement = element;
                break;
            }
        }

        if (matchingElement) {
            // This is the matching element, update its labels
            console.log('Updating UI for element:', selector, x, y);

            // Update the element's dataset
            matchingElement.dataset.labeled = 'true';
            matchingElement.dataset.elementType = Array.isArray(item.label) ?
                item.label[item.label.length - 1] : item.label;
            matchingElement.dataset.labels = JSON.stringify(Array.isArray(item.label) ?
                item.label : [item.label]);

            // Set isAccepted property if it exists in the item
            if (item.isAccepted) {
                matchingElement.dataset.isAccepted = item.isAccepted;
            }

            // Update the visual state
            updateElementVisualState(matchingElement);
        } else {
            // If no matching element found, we might need to reload the elements
            console.log('No matching element found for coordinates:', x, y, width, height);
        }
    }

    // Send updated label data to parent to update progress bar
    sendLabelDataToParent();
}

// Function to call the append API
async function appendLabels(filename, data) {
    try {
        // Get the API base URL using the helper function
        const apiBaseUrl = getApiBaseUrl();

        // Use the annotatedElementsData that was loaded when the page initialized
        // For each item in our data array, try to find matching existing data
        for (let i = 0; i < data.length; i++) {
            const selector = data[i].selector;
            const x = data[i].x;
            const y = data[i].y;
            const width = data[i].width;
            const height = data[i].height;

            // Only calculate area if it's not already present
            if (width !== undefined && height !== undefined && data[i].area === undefined) {
                data[i].area = width * height;
            }

            // Parse the selector to extract tag, class, and id information
            const selectorInfo = parseSelector(selector);

            // Create a key for the current item if coordinates are provided
            let itemKey = null;
            if (x !== undefined && y !== undefined && width !== undefined && height !== undefined) {
                itemKey = createBoundingBoxKey(data[i]);
            }

            // Find the matching element in annotatedElementsData by coordinates key if available, otherwise by selector
            let existingItem = null;
            if (itemKey) {
                // If we have coordinates, find by key
                existingItem = annotatedElementsData.find(item => createBoundingBoxKey(item) === itemKey);
            } else {
                // If no coordinates, find by selector only
                existingItem = annotatedElementsData.find(item => item.selector === selector);
            }

            if (existingItem) {
                // Keep the original coordinates and properties, but update the label
                // This preserves the unique properties of each element
                if (x === undefined) {
                    // If coordinates weren't provided in the data, use the existing ones
                    data[i] = {
                        ...existingItem,
                        label: data[i].label, // Keep our new labels
                        timestamp: new Date().toISOString() // Update timestamp
                    };
                } else {
                    // If coordinates were provided, keep them and update other properties
                    data[i] = {
                        ...existingItem,
                        x: x, // Keep the provided coordinates
                        y: y,
                        width: width,
                        height: height,
                        label: data[i].label, // Keep our new labels
                        timestamp: new Date().toISOString() // Update timestamp
                    };

                    // Preserve the existing area if it exists, otherwise calculate it
                    if (existingItem.area !== undefined) {
                        data[i].area = existingItem.area;
                    } else if (width !== undefined && height !== undefined) {
                        data[i].area = width * height;
                    }
                }

                // Ensure tag and class are set correctly
                if (!data[i].tag || data[i].tag === '') {
                    data[i].tag = selectorInfo.tag;
                }
                if (!data[i].class || data[i].class === '') {
                    data[i].class = selectorInfo.class;
                }
                if (!data[i].id || data[i].id === '') {
                    data[i].id = selectorInfo.id;
                }

                console.log('Found existing data for selector:', selector, 'with coordinates:', data[i].x, data[i].y);
            } else {
                console.log('No existing data found for selector and coordinates:', selector, x, y);

                // If no existing item found with matching coordinates, check if we already have coordinates
                if (x !== undefined && y !== undefined && width !== undefined && height !== undefined) {
                    // We already have coordinates, just need to add tag, class, and id
                    const tag = selectorInfo.tag;
                    const elementClass = selectorInfo.class;
                    const id = selectorInfo.id;

                    // Update the data with these properties
                    data[i] = {
                        ...data[i], // Keep existing properties including coordinates
                        tag: tag,
                        class: elementClass,
                        id: id,
                        timestamp: new Date().toISOString()
                    };
                    console.log('Using provided coordinates for selector:', selector, 'with coordinates:', x, y, width, height);
                } else {
                    // If no coordinates provided, try to find the element in the DOM
                    const element = document.querySelector(`.box[data-selector="${selector}"]`);
                    if (element) {
                        // Get the original coordinates from the dataset
                        const x = parseInt(element.dataset.originalX || element.style.left);
                        const y = parseInt(element.dataset.originalY || element.style.top);
                        const width = parseInt(element.dataset.originalWidth || element.style.width);
                        const height = parseInt(element.dataset.originalHeight || element.style.height);

                        // Get tag, class, and id from the selector if not available in dataset
                        const tag = element.dataset.tag || selectorInfo.tag;
                        const elementClass = element.dataset.class || selectorInfo.class;
                        const id = element.dataset.id || selectorInfo.id;

                        // Update the data with these properties
                        data[i] = {
                            x: x,
                            y: y,
                            width: width,
                            height: height,
                            tag: tag,
                            class: elementClass,
                            id: id,
                            selector: selector,
                            label: data[i].label,
                            timestamp: new Date().toISOString()
                        };
                        console.log('Created data from DOM element for selector:', selector, 'with coordinates:', x, y, width, height);
                    }
                }
            }
        }

        // Now call the append API with the enhanced data
        const response = await fetch(`${apiBaseUrl}/proxy/append`, {
            method: "POST",
            headers: { "Content-Type": "application/json" },
            body: JSON.stringify({
                filename: filename,
                data: data
            })
        });

        if (!response.ok) {
            const errorData = await response.json();
            throw new Error(`API error: ${errorData.error || response.statusText}`);
        }

        console.log(`Data appended to ${filename} successfully via API.`);

        // Update the annotated elements data in memory and refresh the UI
        updateAnnotatedElementsData(data);

        // Send updated label data to parent to update progress bar
        sendLabelDataToParent();

        return true;
    } catch (error) {
        console.error(`Error appending data to ${filename}:`, error);
        alert(`Error appending data to ${filename}. Check console for details.`);
        return false;
    }
}

// Setup component menu and event handlers
function setupComponentMenu() {
    console.log('Setting up component menu...');

    // Setup checkbox change handlers
    document.querySelectorAll('.component-item input[type="checkbox"]').forEach(checkbox => {
        checkbox.addEventListener('change', () => {
            const elementType = checkbox.dataset.type;
            if (checkbox.checked) {
                // Add to selected labels if not already included
                if (!selectedLabels.includes(elementType)) {
                    selectedLabels.push(elementType);
                }
            } else {
                // Remove from selected labels
                const index = selectedLabels.indexOf(elementType);
                if (index !== -1) {
                    selectedLabels.splice(index, 1);
                }
            }
            console.log('Selected labels:', selectedLabels);
        });
    });

    // Setup save button
    document.getElementById('saveLabelsBtn').addEventListener('click', async () => {
        if (currentElement && selectedLabels.length > 0) {
            console.log('Saving labels:', selectedLabels);

            // Store a reference to the clicked element that won't be cleared by hideComponentMenu
            lastClickedElement = currentElement;
            console.log('Stored lastClickedElement with coordinates:',
                lastClickedElement.dataset.originalX,
                lastClickedElement.dataset.originalY);

            // Get the element data - just need the selector for now
            const selector = currentElement.dataset.selector;

            // Store current selector and labels for the multi-label popup
            currentSelector = selector;
            currentLabelType = selectedLabels;

            // Show the multi-label popup first
            showMultiLabelPopup();

            // The actual API call will be done in the No button click handler

            // Don't hide the component menu
            // hideComponentMenu();
        } else if (selectedLabels.length === 0) {
            alert('Please select at least one label.');
        }
    });

    // No color selection needed - using fixed red color for all annotated elements

    // Setup search functionality
    searchInput.addEventListener('input', () => {
        const searchTerm = searchInput.value.toLowerCase();
        document.querySelectorAll('.component-item').forEach(item => {
            const itemText = item.textContent.toLowerCase();
            item.style.display = itemText.includes(searchTerm) ? 'block' : 'none';
        });
    });

    // Setup "Unsure" button
    document.getElementById('markAsUnsure').addEventListener('click', async () => {
        if (currentElement) {
            // Store a reference to the clicked element that won't be cleared by hideComponentMenu
            lastClickedElement = currentElement;
            console.log('Stored lastClickedElement with coordinates:',
                lastClickedElement.dataset.originalX,
                lastClickedElement.dataset.originalY);

            // Get the element data - just need the selector for now
            const selector = currentElement.dataset.selector;

            // Store current selector and label for the multi-label popup
            currentSelector = selector;
            currentLabelType = 'Mark as "Unsure"';

            // Show the multi-label popup first
            showMultiLabelPopup();

            // Don't hide the component menu
            // hideComponentMenu();
        }
    });

    // Setup "Not UI Element" button
    document.getElementById('markAsNotUI').addEventListener('click', async () => {
        if (currentElement) {
            // Store a reference to the clicked element that won't be cleared by hideComponentMenu
            lastClickedElement = currentElement;
            console.log('Stored lastClickedElement with coordinates:',
                lastClickedElement.dataset.originalX,
                lastClickedElement.dataset.originalY);

            // Get the element data - just need the selector for now
            const selector = currentElement.dataset.selector;

            // Store current selector and label for the multi-label popup
            currentSelector = selector;
            currentLabelType = 'Not a UI Element';

            // Show the multi-label popup first
            showMultiLabelPopup();

            // Don't hide the component menu
            // hideComponentMenu();
        }
    });

    // Add keyboard event listener to close menu with Escape key and remove highlighting
    document.addEventListener('keydown', (e) => {
        if (e.key === 'Escape' || e.key === 'Esc' || e.keyCode === 27) {
            // Close component menu if it's open
            if (componentMenu.style.display === 'block') {
                hideComponentMenu();
            }

            // Remove highlighting from all boxes
            removeAllHighlights();
        }
    });

    // Setup multi-label popup buttons
    document.getElementById('yesBtn').addEventListener('click', async () => {
        console.log('Yes button clicked');
        if (currentSelector && currentLabelType) {
            console.log('Finding all elements with selector:', currentSelector);

            // Get all elements with the same selector
            const elements = Array.from(document.querySelectorAll(`.box[data-selector="${currentSelector}"]`));
            console.log('Found elements with same selector:', elements.length);

            if (elements.length > 0) {
                // Create an array to hold all the data objects for the API call
                const dataArray = [];

                // Process each element with the same selector
                for (const element of elements) {
                    // Get the original coordinates from the dataset
                    const x = parseInt(element.dataset.originalX || element.style.left);
                    const y = parseInt(element.dataset.originalY || element.style.top);
                    const width = parseInt(element.dataset.originalWidth || element.style.width);
                    const height = parseInt(element.dataset.originalHeight || element.style.height);

                    // Parse the selector to extract tag, class, and id information
                    const selectorInfo = parseSelector(currentSelector);
                    const tag = selectorInfo.tag;
                    const elementClass = selectorInfo.class;
                    const id = selectorInfo.id;

                    // Create a data object for this element with all required properties
                    // Add BulkAnnotated: "Yes" field for the Yes button click
                    let labelValue = currentLabelType;

                    // Convert button text to appropriate label values
                    if (currentLabelType === 'Mark as "Unsure"') {
                        labelValue = 'Unknown Element';
                    } else if (currentLabelType === 'Not a UI Element') {
                        labelValue = 'Non-UI Element';
                    }

                    // Check if this element already has an area in the dataset
                    let area;
                    if (element.dataset.area) {
                        area = parseInt(element.dataset.area);
                    } else {
                        area = width * height;
                    }

                    const dataObj = {
                        selector: currentSelector,
                        label: labelValue,
                        x: x,
                        y: y,
                        width: width,
                        height: height,
                        area: area, // Use existing area if available, otherwise calculate it
                        tag: tag,
                        class: elementClass,
                        id: id,
                        timestamp: new Date().toISOString(),
                        BulkAnnotated: "Yes"  // Add this field to indicate bulk annotation
                    };

                    // Add to our data array
                    dataArray.push(dataObj);

                    // Apply the labels to the element in the UI
                    if (Array.isArray(currentLabelType)) {
                        // If currentLabelType is an array (multiple labels)
                        for (const label of currentLabelType) {
                            applyLabelToElement(element, label);
                        }
                    } else if (currentLabelType === 'Mark as "Unsure"') {
                        // If it's the "Mark as Unsure" button
                        applyLabelToElement(element, 'Unknown Element');
                    } else if (currentLabelType === 'Not a UI Element') {
                        // If it's the "Not a UI Element" button
                        applyLabelToElement(element, 'Non-UI Element');
                    } else {
                        // If currentLabelType is a single label
                        applyLabelToElement(element, currentLabelType);
                    }
                }

                // Get the filename from the URL
                const urlParams = new URLSearchParams(window.location.search);
                const jsonName = urlParams.get('json') || 'annotation_object.json';

                // Make a single API call for all elements with the same selector
                console.log('Making API call for all elements with same selector');
                const success = await appendLabels(jsonName, dataArray);

                if (success) {
                    console.log('Successfully saved all elements with same selector');

                    // Clear selected labels and checkboxes
                    selectedLabels = [];
                    document.querySelectorAll('.component-item input[type="checkbox"]').forEach(checkbox => {
                        checkbox.checked = false;
                    });

                    // If we're in review mode (left menu is visible), update the left accordion menu in real-time
                    if (isSubmitted && document.getElementById('leftComponentMenu').style.display === 'flex') {
                        // Re-render the left accordion menu to reflect the updated labels
                        renderLeftAccordionComponentList();
                    }
                }
            }

            hideMultiLabelPopup();
        }
    });

    document.getElementById('noBtn').addEventListener('click', async () => {
        console.log('No button clicked');

        // Make the API call when No is clicked
        if (currentSelector && currentLabelType) {
            console.log('Making API call after No button clicked');

            // We need to find the specific clicked element to get its coordinates
            // This is stored in the lastClickedElement variable when the Save Labels button is clicked
            let clickedElement = null;

            // First try to use the lastClickedElement which should have been set when Save Labels was clicked
            if (lastClickedElement && lastClickedElement.dataset.selector === currentSelector) {
                clickedElement = lastClickedElement;
                console.log('Using lastClickedElement for coordinates:',
                    clickedElement.dataset.originalX,
                    clickedElement.dataset.originalY);
            } else if (currentElement && currentElement.dataset.selector === currentSelector) {
                // Try currentElement as a fallback
                clickedElement = currentElement;
                console.log('Using currentElement for coordinates');
            } else {
                // If neither is available, find the first element with the selector
                // This is a fallback, but we should have the lastClickedElement in most cases
                clickedElement = document.querySelector(`.box[data-selector="${currentSelector}"]`);
                console.log('Using fallback element for coordinates');
            }

            if (clickedElement) {
                // Get the original coordinates from the dataset
                const x = parseInt(clickedElement.dataset.originalX || clickedElement.style.left);
                const y = parseInt(clickedElement.dataset.originalY || clickedElement.style.top);
                const width = parseInt(clickedElement.dataset.originalWidth || clickedElement.style.width);
                const height = parseInt(clickedElement.dataset.originalHeight || clickedElement.style.height);

                console.log('Using coordinates for clicked element:', x, y, width, height);

                // Parse the selector to extract tag, class, and id information
                const selectorInfo = parseSelector(currentSelector);
                const tag = selectorInfo.tag;
                const elementClass = selectorInfo.class;
                const id = selectorInfo.id;

                // Create the data object with all required properties
                let labelValue = currentLabelType;

                // Convert button text to appropriate label values
                if (currentLabelType === 'Mark as "Unsure"') {
                    labelValue = 'Unknown Element';
                } else if (currentLabelType === 'Not a UI Element') {
                    labelValue = 'Non-UI Element';
                }

                // Check if this element already has an area in the dataset
                let area;
                if (clickedElement.dataset.area) {
                    area = parseInt(clickedElement.dataset.area);
                } else {
                    area = width * height;
                }

                const data = [
                    {
                        selector: currentSelector,
                        label: labelValue,
                        x: x,
                        y: y,
                        width: width,
                        height: height,
                        area: area, // Use existing area if available, otherwise calculate it
                        tag: tag,
                        class: elementClass,
                        id: id,
                        timestamp: new Date().toISOString()
                    }
                ];

                // Get the filename from the URL
                const urlParams = new URLSearchParams(window.location.search);
                const jsonName = urlParams.get('json') || 'annotation_object.json';

                // Call the append API
                const success = await appendLabels(jsonName, data);

                if (success) {
                    // Apply the labels to the element in the UI
                    if (Array.isArray(currentLabelType)) {
                        // If currentLabelType is an array (multiple labels)
                        for (const label of currentLabelType) {
                            applyLabelToElement(clickedElement, label);
                        }
                    } else if (currentLabelType === 'Mark as "Unsure"') {
                        // If it's the "Mark as Unsure" button
                        applyLabelToElement(clickedElement, 'Unknown Element');
                    } else if (currentLabelType === 'Not a UI Element') {
                        // If it's the "Not a UI Element" button
                        applyLabelToElement(clickedElement, 'Non-UI Element');
                    } else {
                        // If currentLabelType is a single label
                        applyLabelToElement(clickedElement, currentLabelType);
                    }

                    // Clear selected labels and checkboxes
                    selectedLabels = [];
                    document.querySelectorAll('.component-item input[type="checkbox"]').forEach(checkbox => {
                        checkbox.checked = false;
                    });

                    // If we're in review mode (left menu is visible), update the left accordion menu in real-time
                    if (isSubmitted && document.getElementById('leftComponentMenu').style.display === 'flex') {
                        // Re-render the left accordion menu to reflect the updated labels
                        renderLeftAccordionComponentList();
                    }
                }
            } else {
                console.error('Could not find the clicked element with selector:', currentSelector);
                alert('Error: Could not find the element to label.');
            }
        }

        // Hide the popup
        hideMultiLabelPopup();
    });

    // Also close popup when clicking on the overlay
    document.getElementById('popupOverlay').addEventListener('click', () => {
        console.log('Overlay clicked, closing popup');
        hideMultiLabelPopup();
    });

    console.log('Component menu setup complete.');
}

// Function to remove all highlights and reset opacity
function removeAllHighlights() {
    // Remove highlighted class from all boxes
    document.querySelectorAll('.box.highlighted').forEach(box => {
        box.classList.remove('highlighted');
    });

    // Remove highlighting-active class from wrapper to restore normal opacity
    const wrapper = document.getElementById('wrapper');
    wrapper.classList.remove('highlighting-active');
}

// Function to apply highlighting and dimming
function applyHighlighting(element, selector) {
    try {
        // First remove all existing highlights
        document.querySelectorAll('.box.highlighted').forEach(box => {
            box.classList.remove('highlighted');
        });

        // Add highlighting-active class to wrapper to reduce opacity of all boxes
        const wrapper = document.getElementById('wrapper');
        wrapper.classList.add('highlighting-active');
        element.classList.add('highlighted');

        // Add highlighted class to boxes with matching selector
        // document.querySelectorAll(`.box[data-selector="${selector}"]`).forEach(box => {
        //     box.classList.add('highlighted');
        // });
    } catch (err) {
        console.error('Error applying highlighting:', err);
    }
}

// Hide component menu
function hideComponentMenu() {
    // Don't hide the component menu, just clear the current element
    // componentMenu.style.display = 'none';
    currentElement = null;
}

// Send label data to parent frame if needed
function sendLabelDataToParent() {
    // Use the original number of elements from sortedElementsData
    // This ensures we don't count duplicates when elements have multiple labels
    const totalElements = sortedElementsData.length;
    console.log('Using total elements from sortedElementsData:', totalElements);

    // Calculate the total number of labeled elements
    const labeledElementsCount = document.querySelectorAll('.box[data-labeled="true"]').length;

    // Recalculate the breakdown of labels by type to ensure accuracy
    // Reset all counts to 0
    Object.keys(labeledElements).forEach(key => {
        labeledElements[key] = 0;
    });

    // Count each labeled element by its type
    document.querySelectorAll('.box[data-labeled="true"]').forEach(element => {
        if (element.dataset.labels) {
            try {
                const labels = JSON.parse(element.dataset.labels);
                labels.forEach(label => {
                    if (labeledElements.hasOwnProperty(label)) {
                        labeledElements[label]++;
                    }
                });
            } catch (e) {
                console.error('Error parsing labels:', e);
                // Fallback to primary label
                if (element.dataset.elementType && labeledElements.hasOwnProperty(element.dataset.elementType)) {
                    labeledElements[element.dataset.elementType]++;
                }
            }
        } else if (element.dataset.elementType && labeledElements.hasOwnProperty(element.dataset.elementType)) {
            labeledElements[element.dataset.elementType]++;
        }
    });

    // Calculate the number of accepted items for review status
    let acceptedCount = 0;
    if (isSubmitted) {
        // Count elements that have isAccepted="Yes"
        acceptedCount = annotatedElementsData.filter(item => item.isAccepted === "Yes").length;

        // Also check DOM elements with data-is-accepted="Yes" as a fallback
        const acceptedBoxes = document.querySelectorAll('.box[data-is-accepted="Yes"]').length;

        // Use the higher count to ensure we don't miss any accepted elements
        acceptedCount = Math.max(acceptedCount, acceptedBoxes);

        console.log(`Review status: ${acceptedCount} accepted out of ${totalElements} total elements (using sortedElementsData.length)`);
    }

    // Create the summary object with the recalculated breakdown
    const summary = {
        total: totalElements,
        labeled: labeledElementsCount,
        breakdown: { ...labeledElements },
        // Include review status information
        review: {
            // Use the total number of elements, not the number of labeled elements
            total: totalElements,
            accepted: acceptedCount,
            // Calculate percentage for progress bar based on total elements
            percentage: totalElements > 0 ? Math.round((acceptedCount / totalElements) * 100) : 0
        }
    };

    console.log('Label summary:', summary);

    // If in iframe, send message to parent
    if (window.parent !== window) {
        window.parent.postMessage({
            type: 'LABEL_SUMMARY',
            data: summary
        }, '*');
    }
}


// Toggle popup visibility - no changes
// document.getElementById("toggleListBtn").addEventListener("click", () => {
//   const modal = document.getElementById("elementListModal");
//   modal.style.display = modal.style.display === "none" ? "block" : "none";
// });

// Helper function to get the API base URL from environment variables
function getApiBaseUrl() {
    // Always use the current hostname for API connections to avoid login issues
    // This ensures WebSocket connections use network host instead of localhost
    const hostname = window.location.hostname;
    console.log('Current hostname in label.html:', hostname);

    const apiUrl = hostname !== 'localhost' && hostname !== '127.0.0.1'
        ? `http://${hostname}:5001/api`
        : 'http://localhost:5001/api';

    console.log('Using API URL:', apiUrl);
    return apiUrl;
}

// State variables for zoom and pan
let currentZoomLevel = 100;
let panX = 0;
let panY = 0;
let isDragging = false;
let isMouseDown = false;
let lastMouseX = 0;
let lastMouseY = 0;
let mouseDownX = 0;
let mouseDownY = 0;
let initialScale = 1;
let startPanX = 0;
let startPanY = 0;
let dragThreshold = 5; // Pixels to move before considering it a drag
let recentlyDragged = false; // Flag to prevent click events right after drag

// Debug mode
const DEBUG = false; // Set to true to show debug overlay

// Update debug overlay
function updateDebugOverlay() {
    if (!DEBUG) return;

    const overlay = document.getElementById('debugOverlay');
    if (!overlay) return;

    overlay.style.display = 'block';
    document.getElementById('debugPan').textContent = `X: ${Math.round(panX)}, Y: ${Math.round(panY)}`;
    document.getElementById('debugZoom').textContent = `${currentZoomLevel}%`;
    document.getElementById('debugMouseDown').textContent = isMouseDown ? 'true' : 'false';
    document.getElementById('debugDragging').textContent = isDragging ? 'true' : 'false';
}

// Update mouse position in debug overlay
function updateMousePosition(x, y) {
    if (!DEBUG) return;

    const debugMousePos = document.getElementById('debugMousePos');
    if (debugMousePos) {
        debugMousePos.textContent = `${Math.round(x)}, ${Math.round(y)}`;
    }
}

// Log debug event
function logDebugEvent(event) {
    if (!DEBUG) return;

    const debugLastEvent = document.getElementById('debugLastEvent');
    if (debugLastEvent) {
        debugLastEvent.textContent = event;
    }
    updateDebugOverlay();
}

// Function to update the transform with current zoom and pan values
function updateTransform() {
    const container = document.getElementById('transform-container');
    const wrapper = document.getElementById('wrapper');

    if (!container || !wrapper) {
        console.error('Container elements not found!');
        return;
    }

    // Apply both scale and translation to transform-container
    container.style.transform = `translate(${panX}px, ${panY}px) scale(${currentZoomLevel / 100})`;

    // Remove any transform from wrapper
    wrapper.style.transform = 'none';

    // Update debug info
    if (DEBUG) {
        console.log('Transform applied:', container.style.transform);
        updateDebugOverlay();
    }
}

// Function to set zoom level and adjust elements
function setZoomLevel(zoomLevel, mouseX = null, mouseY = null) {
    console.log(`Setting zoom level to ${zoomLevel}%`);

    // Calculate zoom change
    const prevZoom = currentZoomLevel / 100;
    const newZoom = zoomLevel / 100;

    // If mouse coordinates are provided, use them as focal point
    if (mouseX !== null && mouseY !== null) {
        // Adjust pan position to keep point under mouse cursor
        const wrapper = document.getElementById('wrapper');
        const rect = wrapper.getBoundingClientRect();

        // Calculate position relative to wrapper
        const relX = (mouseX - rect.left - panX) / prevZoom;
        const relY = (mouseY - rect.top - panY) / prevZoom;

        // Adjust pan to maintain position under cursor
        panX = panX + mouseX - (relX * newZoom + panX + rect.left);
        panY = panY + mouseY - (relY * newZoom + panY + rect.top);
    }

    currentZoomLevel = zoomLevel;
    updateTransform();
    console.log(`Zoom applied: ${zoomLevel}%, Pan: (${panX}, ${panY})`);
}

// Function to set pan position
function setPanPosition(x, y) {
    panX = x;
    panY = y;
    updateTransform();
    console.log('Pan position set to:', x, y);
    updateDebugOverlay();

    // Adjust bounding boxes after pan, similar to how it's done after zoom
    setTimeout(() => {
        adjustBoundingBoxes();
    }, 100);
}

// Function to adjust bounding boxes based on current image dimensions
function adjustBoundingBoxes() {
    console.log('Adjusting bounding boxes to match current image dimensions');

    // Get the current image dimensions
    const currentImageWidth = screenshot.width;
    const originalImageWidth = screenshot.naturalWidth;

    // Calculate the scale factor based on the current image width vs original width
    const scaleFactor = currentImageWidth / originalImageWidth;
    console.log(`Scale factor: ${scaleFactor}, Current width: ${currentImageWidth}, Original width: ${originalImageWidth}`);

    // Get all bounding boxes
    const boxes = document.querySelectorAll('.box');

    boxes.forEach(box => {
        // Get the original coordinates and dimensions from the dataset
        const originalX = parseInt(box.dataset.originalX || box.style.left);
        const originalY = parseInt(box.dataset.originalY || box.style.top);
        const originalWidth = parseInt(box.dataset.originalWidth || box.style.width);
        const originalHeight = parseInt(box.dataset.originalHeight || box.style.height);

        // Store original values if not already stored
        if (!box.dataset.originalX) {
            box.dataset.originalX = originalX;
            box.dataset.originalY = originalY;
            box.dataset.originalWidth = originalWidth;
            box.dataset.originalHeight = originalHeight;
        }

        // Update the box position and dimensions based on the scale factor
        const scaledX = Math.round(originalX * scaleFactor);
        const scaledY = Math.round(originalY * scaleFactor);
        const scaledWidth = Math.round(originalWidth * scaleFactor);
        const scaledHeight = Math.round(originalHeight * scaleFactor);

        box.style.left = `${scaledX}px`;
        box.style.top = `${scaledY}px`;
        box.style.width = `${scaledWidth}px`;
        box.style.height = `${scaledHeight}px`;

        // No scaling for labels
    });

    console.log('Bounding boxes adjusted successfully');

    // Apply current visibility settings after adjusting boxes
    updateBoxVisibility();
}

// Function to update box visibility based on current state
function updateBoxVisibility() {
    console.log('Updating box visibility to:', boxVisibility);
    const boxes = document.querySelectorAll('.box');

    boxes.forEach(box => {
        // Check if the box is annotated by looking for the annotated-box class
        const isAnnotated = box.classList.contains('annotated-box');

        // Log for debugging
        console.log(`Box ${box.dataset.selector}: isAnnotated=${isAnnotated}, current visibility=${boxVisibility}`);

        if (boxVisibility === 'all') {
            // Show all boxes
            box.style.display = 'block';
        } else if (boxVisibility === 'annotated') {
            // Only show annotated boxes
            box.style.display = isAnnotated ? 'block' : 'none';
        } else if (boxVisibility === 'unannotated') {
            // Only show unannotated boxes
            box.style.display = !isAnnotated ? 'block' : 'none';
        }
    });
}

// Variable to track view-only mode
let viewOnlyMode = false;

// Function to set view-only mode
function setViewOnlyMode(isViewOnly) {
    viewOnlyMode = isViewOnly;
    console.log('View-only mode set to:', viewOnlyMode);

    // If in view-only mode, disable all box interactions
    const boxes = document.querySelectorAll('.box');
    boxes.forEach(box => {
        if (viewOnlyMode) {
            // Remove event listeners by cloning and replacing the element
            const newBox = box.cloneNode(true);
            box.parentNode.replaceChild(newBox, box);

            // Remove hover effects in view-only mode
            newBox.style.pointerEvents = 'none';
            newBox.style.cursor = 'default';
        }
    });

    // If in view-only mode, set visibility to 'annotated' only
    if (viewOnlyMode) {
        boxVisibility = 'annotated';
        updateBoxVisibility();
    }
}

// Listen for messages from parent component
window.addEventListener('message', (event) => {
    if (event.data.type === 'GET_LABEL_SUMMARY') {
        sendLabelDataToParent();
    } else if (event.data.type === 'SAVE_LABELS') {
        // You might want to trigger saving all detailedLabelData to server here if needed, but saving is now done on each label application.
        console.log('SAVE_LABELS message received, but saving is now handled on each label application.');
        // If you still need to save all labels at once, you'd need to implement a different save mechanism.
    } else if (event.data.type === 'ADJUST_BOXES') {
        // Adjust bounding boxes when requested by parent
        adjustBoundingBoxes();
    } else if (event.data.type === 'SET_ZOOM') {
        // Set zoom level when requested by parent
        const zoomLevel = event.data.zoomLevel;
        setZoomLevel(zoomLevel);
        // Also adjust boxes after zoom change
        setTimeout(() => {
            adjustBoundingBoxes();
        }, 100);
    } else if (event.data.type === 'RESET_PAN') {
        // Reset pan position when requested by parent
        panX = 0;
        panY = 0;
        startPanX = 0;
        startPanY = 0;
        updateTransform();
        // Also adjust boxes after pan reset
        setTimeout(() => {
            adjustBoundingBoxes();
        }, 100);
    } else if (event.data.type === 'SET_BOX_VISIBILITY') {
        // Update box visibility when requested by parent
        console.log('Received SET_BOX_VISIBILITY message with visibility:', event.data.visibility);
        boxVisibility = event.data.visibility;
        // Update the visibility of all boxes
        updateBoxVisibility();
    } else if (event.data.type === 'SET_VIEW_ONLY_MODE') {
        // Set view-only mode when requested by parent
        setViewOnlyMode(event.data.viewOnly);
    }
});
// Initial loading of labels - this part can be removed as labels are now loaded and applied in loadElements function.
// loadExistingLabels(); // No longer needed here, labels are handled in loadElements and applyLabel

// Setup zoom and pan event listeners
function setupZoomPanEventListeners() {
    const container = document.getElementById('transform-container');
    const wrapper = document.getElementById('wrapper');

    if (!container || !wrapper) {
        console.error('Cannot setup zoom/pan - container or wrapper element not found!');
        return;
    }

    // Add click handlers to test if elements are receiving events
    container.addEventListener('click', (e) => {
        console.log('Container clicked at', e.clientX, e.clientY);
        logDebugEvent(`Container clicked at ${e.clientX},${e.clientY}`);
    });

    wrapper.addEventListener('click', (e) => {
        console.log('Wrapper clicked at', e.clientX, e.clientY);
        logDebugEvent(`Wrapper clicked at ${e.clientX},${e.clientY}`);
    });

    // Prevent default gesture events in Safari
    document.addEventListener('gesturestart', function (e) {
        e.preventDefault();
    }, { passive: false });

    // Mouse wheel zoom
    container.addEventListener('wheel', (e) => {
        e.preventDefault();

        // If ctrl key is pressed, it's likely a pinch gesture from touchpad
        if (e.ctrlKey) {
            const delta = -e.deltaY * 0.01;
            const newZoom = Math.max(10, Math.min(400, currentZoomLevel + delta * 10));

            // Apply zoom with center of viewport as focal point
            setZoomLevel(newZoom, e.clientX, e.clientY);
        } else {
            // Regular wheel zoom
            const delta = -e.deltaY * 0.01;
            const newZoom = Math.max(10, Math.min(400, currentZoomLevel + delta * 10));

            // Apply zoom with mouse position as focal point
            setZoomLevel(newZoom, e.clientX, e.clientY);
        }

        // Adjust bounding boxes after zoom
        setTimeout(() => {
            adjustBoundingBoxes();
        }, 100);
    }, { passive: false });

    // Mouse drag for panning
    const panContainer = document.getElementById('pan-container');

    panContainer.addEventListener('mousedown', (e) => {
        console.log('Mousedown on element:', e.target.tagName, e.target.id, e.target.className);
        logDebugEvent(`mousedown on ${e.target.tagName}#${e.target.id}.${e.target.className}`);

        // Always track mouse down state, regardless of target
        isMouseDown = true;
        mouseDownX = e.clientX;
        mouseDownY = e.clientY;
        lastMouseX = e.clientX;
        lastMouseY = e.clientY;
        startPanX = panX; // Store starting pan position
        startPanY = panY;

        // Don't set isDragging yet - we'll determine that based on movement
        // This allows us to distinguish between clicks and drags

        // // If it's not a box, we can set the cursor immediately
        // if (!e.target.classList || !e.target.classList.contains('box')) {
        //   panContainer.style.cursor = 'grabbing';
        // }

        console.log('Mouse down at', e.clientX, e.clientY);
        logDebugEvent(`Mouse down at ${e.clientX},${e.clientY}`);
        updateDebugOverlay();
    }, { passive: false });

    document.addEventListener('mousemove', (e) => {
        // Always update mouse position for debugging
        updateMousePosition(e.clientX, e.clientY);

        if (isMouseDown) {
            // Calculate total movement since mouse down
            const totalDx = e.clientX - mouseDownX;
            const totalDy = e.clientY - mouseDownY;
            const totalDistance = Math.sqrt(totalDx * totalDx + totalDy * totalDy);

            // If we've moved past the threshold, start dragging
            if (!isDragging && totalDistance > dragThreshold) {
                isDragging = true;
                panContainer.style.cursor = 'grabbing';
                console.log('Drag threshold exceeded, starting pan');
                logDebugEvent(`Drag started (threshold exceeded: ${totalDistance.toFixed(1)}px)`);
                updateDebugOverlay();
            }

            // If we're dragging, update the pan position
            if (isDragging) {
                // Calculate movement since mouse down (not since last move)
                const dx = e.clientX - mouseDownX;
                const dy = e.clientY - mouseDownY;

                // Calculate new pan position based on starting position and total movement
                const newPanX = startPanX + dx;
                const newPanY = startPanY + dy;

                // Adjust pan position
                setPanPosition(newPanX, newPanY);

                e.preventDefault();

                // Only log occasionally to avoid console flooding
                if (Math.abs(dx) > 10 || Math.abs(dy) > 10) {
                    console.log('Panning: dx=', dx, 'dy=', dy, 'new position:', panX, panY);
                }
            }
        }
    }, { passive: false });

    document.addEventListener('mouseup', (e) => {
        // If we were dragging, end the drag
        if (isDragging) {
            console.log('Pan ended');
            logDebugEvent('Pan ended (mouseup)');
            e.preventDefault();

            // Set the recentlyDragged flag to prevent click events
            recentlyDragged = true;

            // Reset the flag after a short delay
            setTimeout(() => {
                recentlyDragged = false;
                console.log('recentlyDragged flag reset');
            }, 50); // 50ms delay should be enough
        } else if (isMouseDown) {
            // If mouse was down but we weren't dragging, it was a click
            console.log('Click detected (no drag)');
            logDebugEvent('Click detected (no drag)');
        }

        // Reset all mouse state
        isDragging = false;
        isMouseDown = false;
        panContainer.style.cursor = 'grab';
        updateDebugOverlay();
    });

    document.addEventListener('mouseleave', (e) => {
        // Reset all mouse state when mouse leaves the window
        if (isDragging || isMouseDown) {
            isDragging = false;
            isMouseDown = false;
            panContainer.style.cursor = 'grab';
            console.log('Mouse left window, pan ended');
            logDebugEvent('Pan ended (mouseleave)');
            updateDebugOverlay();
            e.preventDefault();
        }
    });

    // Touch events for mobile devices
    let touchStartX, touchStartY;
    let initialTouchDistance = 0;

    panContainer.addEventListener('touchstart', (e) => {
        if (e.touches.length === 1) {
            // Single touch - prepare for panning (but don't start dragging yet)
            isMouseDown = true;
            const touch = e.touches[0];
            mouseDownX = touch.clientX;
            mouseDownY = touch.clientY;
            lastMouseX = touch.clientX;
            lastMouseY = touch.clientY;
            startPanX = panX; // Store starting pan position
            startPanY = panY;
            updateMousePosition(touch.clientX, touch.clientY);
            logDebugEvent(`Touch start at ${touch.clientX},${touch.clientY}`);
            updateDebugOverlay();
            e.preventDefault();
        } else if (e.touches.length === 2) {
            // Two finger touch - prepare for pinch zoom
            e.preventDefault();
            const touch1 = e.touches[0];
            const touch2 = e.touches[1];

            // Calculate the midpoint between the two touches
            touchStartX = (touch1.clientX + touch2.clientX) / 2;
            touchStartY = (touch1.clientY + touch2.clientY) / 2;

            // Calculate the initial distance between touches for pinch zoom
            initialTouchDistance = Math.hypot(
                touch2.clientX - touch1.clientX,
                touch2.clientY - touch1.clientY
            );

            initialScale = currentZoomLevel / 100;
        }
    }, { passive: false });

    panContainer.addEventListener('touchmove', (e) => {
        if (e.touches.length === 1) {
            const touch = e.touches[0];
            updateMousePosition(touch.clientX, touch.clientY);

            if (isMouseDown) {
                // Calculate total movement since touch start
                const totalDx = touch.clientX - mouseDownX;
                const totalDy = touch.clientY - mouseDownY;
                const totalDistance = Math.sqrt(totalDx * totalDx + totalDy * totalDy);

                // If we've moved past the threshold, start dragging
                if (!isDragging && totalDistance > dragThreshold) {
                    isDragging = true;
                    console.log('Touch drag threshold exceeded, starting pan');
                    logDebugEvent(`Touch drag started (threshold: ${totalDistance.toFixed(1)}px)`);
                    updateDebugOverlay();
                }

                // If we're dragging, update the pan position
                if (isDragging) {
                    // Calculate movement since touch start
                    const dx = touch.clientX - mouseDownX;
                    const dy = touch.clientY - mouseDownY;

                    // Calculate new pan position based on starting position and total movement
                    const newPanX = startPanX + dx;
                    const newPanY = startPanY + dy;

                    // Update pan position
                    setPanPosition(newPanX, newPanY);

                    e.preventDefault();
                }
            }
        } else if (e.touches.length === 2) {
            // Two finger touch - handle pinch zoom
            e.preventDefault();
            const touch1 = e.touches[0];
            const touch2 = e.touches[1];

            // Calculate current distance between touches
            const currentTouchDistance = Math.hypot(
                touch2.clientX - touch1.clientX,
                touch2.clientY - touch1.clientY
            );

            // Calculate scale factor based on the change in distance
            const scaleFactor = currentTouchDistance / initialTouchDistance;

            // Calculate new zoom level
            const newZoom = Math.max(10, Math.min(500, initialScale * scaleFactor * 100));

            // Calculate the midpoint between the two touches
            const touchX = (touch1.clientX + touch2.clientX) / 2;
            const touchY = (touch1.clientY + touch2.clientY) / 2;

            // Apply zoom with the midpoint as focal point
            setZoomLevel(newZoom, touchX, touchY);

            // Adjust bounding boxes after zoom
            setTimeout(() => {
                adjustBoundingBoxes();
            }, 100);
        }
    }, { passive: false });

    panContainer.addEventListener('touchend', (e) => {
        if (isDragging) {
            console.log('Touch pan ended');
            logDebugEvent('Touch pan ended');

            // Set the recentlyDragged flag to prevent click events
            recentlyDragged = true;

            // Reset the flag after a short delay
            setTimeout(() => {
                recentlyDragged = false;
                console.log('recentlyDragged flag reset (touch)');
            }, 50); // 50ms delay should be enough
        } else if (isMouseDown) {
            console.log('Touch tap detected (no drag)');
            logDebugEvent('Touch tap detected (no drag)');
        }

        // Reset touch state
        isDragging = false;
        isMouseDown = false;
        initialTouchDistance = 0;
        updateDebugOverlay();
    });

    // Safari specific gesture events
    panContainer.addEventListener('gesturestart', (e) => {
        e.preventDefault();
        initialScale = currentZoomLevel / 100;
    }, { passive: false });

    panContainer.addEventListener('gesturechange', (e) => {
        e.preventDefault();

        // Calculate new zoom based on gesture scale
        const newZoom = Math.max(10, Math.min(500, initialScale * e.scale * 100));

        // Apply zoom with center of gesture as focal point
        setZoomLevel(newZoom, e.clientX, e.clientY);

        // Adjust bounding boxes
        setTimeout(() => {
            adjustBoundingBoxes();
        }, 100);
    }, { passive: false });

    console.log('Zoom and pan event listeners set up');
}

// Accordion Component List Implementation
let activeLabel = null; // Track the currently active label for filtering
let highlightedElement = null; // Track the currently highlighted element

// Flag to track whether the document is in review status
// Check URL parameters to determine if we're in review mode
const taskStatus = new URLSearchParams(window.location.search).get('status');
let isSubmitted = taskStatus === 'review'; // Set to true if status is 'review'
console.log('Document status:', taskStatus, 'isSubmitted:', isSubmitted);

// Function to render the accordion component list for the right menu
function renderAccordionComponentList() {
    const accordionContainer = document.getElementById('accordionComponentList');

    // If no annotated elements, show a message
    if (!annotatedElementsData || annotatedElementsData.length === 0) {
        accordionContainer.innerHTML = `
          <div class="p-4 text-center text-gray-500">
            No annotated elements found.
          </div>
        `;
        return;
    }

    // Group elements by label
    const groupedElements = {};

    // Process each annotated element
    annotatedElementsData.forEach((element, index) => {
        // Handle both array of labels and single label
        const labels = Array.isArray(element.label) ? element.label : [element.label];

        // Add element to each of its label groups
        labels.forEach(label => {
            if (!label) return; // Skip empty labels

            if (!groupedElements[label]) {
                groupedElements[label] = [];
            }

            // Add element with its index for reference
            groupedElements[label].push({
                ...element,
                id: index, // Use index as id for reference
                displayName: `${label} ${groupedElements[label].length + 1}` // e.g., "Logo 1", "Logo 2"
            });
        });
    });

    // Generate HTML for the accordion
    let accordionHTML = '';

    Object.entries(groupedElements).forEach(([label, elements]) => {
        const isExpanded = false; // Default to collapsed

        accordionHTML += `
          <div class="accordion-group" data-label="${label}">
            <div class="accordion-header" onclick="toggleAccordionGroup('${label}')">
              <div class="flex items-center">
                <span class="transform transition-transform ${isExpanded ? 'rotate-90' : ''}">
                  <svg width="16" height="16" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                    <path d="M9 18L15 12L9 6" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round"/>
                  </svg>
                </span>
                <span class="label-name ml-2" onclick="filterByLabel('${label}', event)">${label}
                <span class="item-count">${elements.length}</span>
                </span>
              </div>
              <div class="flex items-center">
                
                <button class="accept-all-btn" onclick="acceptAllItems('${label}', event)">
                    <svg width="16" height="16" viewBox="0 0 16 16" fill="none" xmlns="http://www.w3.org/2000/svg">
                        <g clip-path="url(#clip0_138_169)">
                        <path d="M14.6663 7.39046V8.0038C14.6655 9.44141 14.2 10.8402 13.3392 11.9917C12.4785 13.1431 11.2685 13.9854 9.88991 14.3931C8.5113 14.8007 7.03785 14.7517 5.68932 14.2535C4.3408 13.7553 3.18944 12.8345 2.40698 11.6285C1.62452 10.4225 1.25287 8.99584 1.34746 7.56134C1.44205 6.12684 1.99781 4.76135 2.93186 3.66851C3.86591 2.57568 5.1282 1.81405 6.53047 1.49723C7.93274 1.1804 9.39985 1.32535 10.713 1.91046" stroke="#008236" stroke-width="1.33333" stroke-linecap="round" stroke-linejoin="round"/>
                        <path d="M14.6667 4.26831L8 10.9416L6 8.94164" stroke="#008236" stroke-width="1.33333" stroke-linecap="round" stroke-linejoin="round"/>
                        <path d="M14.6667 0.931641L8 7.60497L6 5.60497" stroke="#008236" stroke-width="1.33333" stroke-linecap="round" stroke-linejoin="round"/>
                        </g>
                        <defs>
                        <clipPath id="clip0_138_169">
                        <rect width="16" height="16" fill="white"/>
                        </clipPath>
                        </defs>
                    </svg>
                </button>
              </div>
            </div>

            <div class="accordion-content" id="accordion-content-${label}" style="display: ${isExpanded ? 'block' : 'none'}">
              ${elements.map(element => `
                <div class="accordion-item"
                     data-element-id="${element.id}"
                     data-x="${element.x}"
                     data-y="${element.y}"
                     data-width="${element.width}"
                     data-height="${element.height}"
                     data-selector="${element.selector}"
                     onmouseover="highlightBoundingBox(${element.x}, ${element.y}, ${element.width}, ${element.height}, '${element.selector}', false)"
                     onmouseout="unhighlightBoundingBox()"
                     onclick="panToBoxOnClick(${element.x}, ${element.y}, ${element.width}, ${element.height}, '${element.selector}', ${element}, event)">
                  <span class="item-name">${element.displayName}</span>
                  <button class="accept-btn" onclick="acceptItem(${element.id}, event)">Accept</button>
                </div>
              `).join('')}
            </div>
          </div>
        `;
    });

    accordionContainer.innerHTML = accordionHTML;
}

// Function to render the accordion component list for the left menu
function renderLeftAccordionComponentList() {
    const leftAccordionContainer = document.getElementById('leftAccordionComponentList');

    // If no annotated elements, show a message
    if (!annotatedElementsData || annotatedElementsData.length === 0) {
        leftAccordionContainer.innerHTML = `
          <div class="p-4 text-center text-gray-500">
            No annotated elements found.
          </div>
        `;
        return;
    }

    // Group elements by label
    const groupedElements = {};

    // Process each annotated element
    annotatedElementsData.forEach((element, index) => {
        // Handle both array of labels and single label
        const labels = Array.isArray(element.label) ? element.label : [element.label];

        // Add element to each of its label groups
        labels.forEach(label => {
            if (!label) return; // Skip empty labels

            if (!groupedElements[label]) {
                groupedElements[label] = [];
            }

            // Add element with its index for reference
            groupedElements[label].push({
                ...element,
                id: index, // Use index as id for reference
                displayName: `${label} ${groupedElements[label].length + 1}` // e.g., "Logo 1", "Logo 2"
            });
        });
    });

    // Generate HTML for the accordion
    let accordionHTML = '';

    Object.entries(groupedElements).forEach(([label, elements]) => {
        const isExpanded = false; // Default to collapsed for consistency with right menu in review mode

        accordionHTML += `
          <div class="accordion-group" data-label="${label}">
            <div class="accordion-header" onclick="toggleLeftAccordionGroup('${label}')">
              <div class="flex items-center">
                <span class="transform transition-transform ${isExpanded ? 'rotate-90' : ''}">
                  <svg width="16" height="16" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                    <path d="M9 18L15 12L9 6" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round"/>
                  </svg>
                </span>
                <span class="label-name ml-2" onclick="filterByLabel('${label}', event)">${label}
                <span class="item-count">${elements.length}</span>
                </span>
                
              </div>
              <div class="flex items-center">
                
                <button class="accept-all-btn" onclick="acceptAllItems('${label}', event)">
                    <svg width="16" height="16" viewBox="0 0 16 16" fill="none" xmlns="http://www.w3.org/2000/svg">
                        <g clip-path="url(#clip0_138_169)">
                        <path d="M14.6663 7.39046V8.0038C14.6655 9.44141 14.2 10.8402 13.3392 11.9917C12.4785 13.1431 11.2685 13.9854 9.88991 14.3931C8.5113 14.8007 7.03785 14.7517 5.68932 14.2535C4.3408 13.7553 3.18944 12.8345 2.40698 11.6285C1.62452 10.4225 1.25287 8.99584 1.34746 7.56134C1.44205 6.12684 1.99781 4.76135 2.93186 3.66851C3.86591 2.57568 5.1282 1.81405 6.53047 1.49723C7.93274 1.1804 9.39985 1.32535 10.713 1.91046" stroke="#008236" stroke-width="1.33333" stroke-linecap="round" stroke-linejoin="round"/>
                        <path d="M14.6667 4.26831L8 10.9416L6 8.94164" stroke="#008236" stroke-width="1.33333" stroke-linecap="round" stroke-linejoin="round"/>
                        <path d="M14.6667 0.931641L8 7.60497L6 5.60497" stroke="#008236" stroke-width="1.33333" stroke-linecap="round" stroke-linejoin="round"/>
                        </g>
                        <defs>
                        <clipPath id="clip0_138_169">
                        <rect width="16" height="16" fill="white"/>
                        </clipPath>
                        </defs>
                    </svg>
                </button>
              </div>
            </div>

            <div class="accordion-content" id="left-accordion-content-${label}" style="display: ${isExpanded ? 'block' : 'none'}">
              ${elements.map(element => `
                <div class="accordion-item ${element.isAccepted === 'Yes' ? 'accepted-item' : ''}"
                     data-element-id="${element.id}"
                     data-x="${element.x}"
                     data-y="${element.y}"
                     data-width="${element.width}"
                     data-height="${element.height}"
                     data-selector="${element.selector}"
                     data-label="${JSON.stringify(element.label)}"
                     data-accepted="${element.isAccepted === 'Yes' ? 'true' : 'false'}"
                     onmouseover="highlightBoundingBox(${element.x}, ${element.y}, ${element.width}, ${element.height}, '${element.selector}', false)"
                     onmouseout="unhighlightBoundingBox()"
                     onclick="panToBoxOnClick(${element.x}, ${element.y}, ${element.width}, ${element.height}, '${element.selector}',' ${element}', event)"> 
                  <span class="item-name">${element.displayName}</span>
                  <button class="accept-btn ${element.isAccepted === 'Yes' ? 'accepted' : ''}"
                          onclick="acceptItem(${element.id}, event)"
                          ${element.isAccepted === 'Yes' ? 'disabled' : ''}>
                    <svg width="16" height="16" viewBox="0 0 16 16" fill="none" xmlns="http://www.w3.org/2000/svg">
                        <g clip-path="url(#clip0_59_749)">
                        <path d="M14.6663 7.39046V8.0038C14.6655 9.44141 14.2 10.8402 13.3392 11.9917C12.4785 13.1431 11.2685 13.9854 9.88991 14.3931C8.5113 14.8007 7.03785 14.7517 5.68932 14.2535C4.3408 13.7553 3.18944 12.8345 2.40698 11.6285C1.62452 10.4225 1.25287 8.99584 1.34746 7.56134C1.44205 6.12684 1.99781 4.76135 2.93186 3.66851C3.86591 2.57568 5.1282 1.81405 6.53047 1.49723C7.93274 1.1804 9.39985 1.32535 10.713 1.91046M14.6663 2.66665L7.99967 9.33998L5.99967 7.33998" stroke="#008236" stroke-width="1.33333" stroke-linecap="round" stroke-linejoin="round"/>
                        </g>
                        <defs>
                        <clipPath id="clip0_59_749">
                        <rect width="16" height="16" fill="white"/>
                        </clipPath>
                        </defs>
                    </svg>

                  </button>
                </div>
              `).join('')}
            </div>
          </div>
        `;
    });

    leftAccordionContainer.innerHTML = accordionHTML;

    // Make sure the accordion is visible and the original component list is hidden
    leftAccordionContainer.style.display = 'block';
    const leftOriginalList = document.getElementById('leftOriginalComponentList');
    if (leftOriginalList) {
        leftOriginalList.style.display = 'none';
    }
}

// Toggle accordion group expansion for the right menu
function toggleAccordionGroup(label) {
    const content = document.getElementById(`accordion-content-${label}`);
    const header = content.previousElementSibling;
    const arrow = header.querySelector('span:first-child');

    if (content.style.display === 'none') {
        // Expand the accordion
        content.style.display = 'block';
        arrow.classList.add('rotate-90');

        // Update the tooltip text
        header.classList.add('expanded');
    } else {
        // Collapse the accordion
        content.style.display = 'none';
        arrow.classList.remove('rotate-90');

        // Update the tooltip text
        header.classList.remove('expanded');
    }
}

// Toggle accordion group expansion for the left menu
function toggleLeftAccordionGroup(label) {
    const content = document.getElementById(`left-accordion-content-${label}`);
    const header = content.previousElementSibling;
    const arrow = header.querySelector('span:first-child');

    if (content.style.display === 'none') {
        // Expand the accordion
        content.style.display = 'block';
        arrow.classList.add('rotate-90');

        // Update the tooltip text
        header.classList.add('expanded');
    } else {
        // Collapse the accordion
        content.style.display = 'none';
        arrow.classList.remove('rotate-90');

        // Update the tooltip text
        header.classList.remove('expanded');
    }
}

// Filter bounding boxes by label
function filterByLabel(label, event) {
    // Stop event propagation to prevent toggling the accordion
    if (event) {
        event.stopPropagation();
    }

    // Make sure the accordion content for this label is expanded
    const accordionContent = document.getElementById(`accordion-content-${label}`);
    if (accordionContent && accordionContent.style.display === 'none') {
        // Expand the accordion
        toggleAccordionGroup(label);
    }

    // If clicking the same label again, clear the filter
    if (activeLabel === label) {
        activeLabel = null;
        // Show all annotated boxes
        document.querySelectorAll('.box.annotated-box').forEach(box => {
            box.style.display = 'block';
        });
    } else {
        activeLabel = label;

        // Hide all annotated boxes first
        document.querySelectorAll('.box.annotated-box').forEach(box => {
            box.style.display = 'none';
        });

        // Show only boxes with the selected label
        annotatedElementsData.forEach(element => {
            const labels = Array.isArray(element.label) ? element.label : [element.label];

            if (labels.includes(label)) {
                // Find the box with matching coordinates
                const selector = element.selector;
                const x = element.x;
                const y = element.y;

                // Find boxes with matching selector and coordinates
                document.querySelectorAll(`.box[data-selector="${selector}"]`).forEach(box => {
                    const boxX = parseInt(box.dataset.originalX);
                    const boxY = parseInt(box.dataset.originalY);

                    // If coordinates match (with small tolerance for rounding), show the box
                    if (Math.abs(boxX - x) <= 2 && Math.abs(boxY - y) <= 2) {
                        box.style.display = 'block';
                    }
                });
            }
        });
    }
}

// Highlight a bounding box when hovering over an item
// If shouldPan is true, also pan to make it visible
function highlightBoundingBox(x, y, width, height, selector, shouldPan = false) {
    // Remove highlight from previous element
    unhighlightBoundingBox();

    // Find the box with matching coordinates and selector
    document.querySelectorAll(`.box[data-selector="${selector}"]`).forEach(box => {
        const boxX = parseInt(box.dataset.originalX);
        const boxY = parseInt(box.dataset.originalY);

        // If coordinates match (with small tolerance for rounding), highlight the box
        if (Math.abs(boxX - x) <= 2 && Math.abs(boxY - y) <= 2) {
            box.classList.add('highlighted-box');
            highlightedElement = box;

            // Pan to make the box visible only if shouldPan is true
            if (shouldPan) {
                panToElement(box);
            }
        }
    });
}

// Function to handle clicking on an accordion item
function panToBoxOnClick(x, y, width, height, selector, element, event) {
    // recentlyDragged = true
    if (recentlyDragged) {
        console.log('Click ignored - happened right after drag');
        return;
    }

    // Prevent the click from triggering parent elements

    if (event) {
        event.stopPropagation();
    }

    // First highlight the box (without panning)
    // highlightBoundingBox(x, y, width, height, selector, false);
    handleElementClick(event, element, true);

    // Then find the box and pan to it
    document.querySelectorAll(`.box[data-selector="${selector}"]`).forEach(box => {
        const boxX = parseInt(box.dataset.originalX);
        const boxY = parseInt(box.dataset.originalY);

        // If coordinates match, pan to the box
        if (Math.abs(boxX - x) <= 2 && Math.abs(boxY - y) <= 2 && highlightedElement) {
            // Add a visual indicator that we're panning to this box
            box.classList.add('panning-to-box');

            // Remove the animation class after it completes
            setTimeout(() => {
                box.classList.remove('panning-to-box');
            }, 1500);

            // Pan to the box
            panToElement(box);

            // Set this box as the current element for the right-side component menu
            currentElement = box;

            // Get the current labels from the box
            let currentLabels = [];
            try {
                currentLabels = JSON.parse(box.dataset.labels || '[]');
            } catch (e) {
                console.error('Error parsing labels:', e);
                if (box.dataset.elementType) {
                    currentLabels = [box.dataset.elementType];
                }
            }

            // Update the current labels section in the right menu
            updateCurrentLabelsSection(currentLabels);

            // Update the save button text for annotated boxes
            const saveButton = document.getElementById('saveLabelsBtn');
            const unsureButton = document.getElementById('markAsUnsure');


            // Pre-select the checkboxes for the current labels
            document.querySelectorAll('.component-item input[type="checkbox"]').forEach(checkbox => {
                checkbox.checked = false;
            });
            selectedLabels = [];

            currentLabels.forEach(label => {
                // Find the checkbox with the matching data-type
                const checkbox = document.querySelector(`.component-item input[data-type="${label}"]`);
                if (checkbox) {
                    checkbox.checked = true;
                    // Add to selected labels array
                    if (!selectedLabels.includes(label)) {
                        selectedLabels.push(label);
                    }
                }
            });

            // Make sure the right component menu is visible
            const componentMenu = document.getElementById('componentMenu');
            if (componentMenu) {
                componentMenu.style.display = 'block';
            }

            // Apply highlighting to all boxes with the same selector
            applyHighlighting(selector);
        }
    });
}

// Function to check if an element is fully visible in the viewport with margin
function isElementFullyVisible(element) {
    // Get the pan container
    const panContainer = document.getElementById('pan-container');

    if (!panContainer) {
        console.error('Cannot check visibility - pan container not found');
        return false;
    }

    // Get the viewport rect
    const viewportRect = panContainer.getBoundingClientRect();

    // Get the element's rect
    const elementRect = element.getBoundingClientRect();

    // Add a margin (in pixels) to ensure the element is comfortably visible
    const margin = 50;

    // Check if the element is fully visible with margin
    isVisible = elementRect.left >= viewportRect.left + margin && elementRect.right <= viewportRect.right - margin && elementRect.top >= viewportRect.top + margin && elementRect.bottom <= viewportRect.bottom - margin
    return (
        isVisible
    );
}

// Function to pan the image to make an element visible
function panToElement(element) {
    // Get the pan container and transform container
    // const panContainer = document.getElementById('pan-container');
    const transformContainer = document.getElementById('pan-container');

    if (!transformContainer) {
        console.error('Cannot pan - container elements not found');
        return;
    }

    // Check if the element is already fully visible
    if (isElementFullyVisible(element)) {
        console.log('Element is already visible, no need to pan');
        return;
    }

    // Get the viewport dimensions
    const viewportWidth = transformContainer.clientWidth;
    const viewportHeight = transformContainer.clientHeight;

    // Get the element's position and dimensions
    const elementRect = element.getBoundingClientRect();

    // Calculate the center of the element
    const elementCenterX = elementRect.left + elementRect.width / 2;
    const elementCenterY = elementRect.top + elementRect.height / 2;

    // Calculate the center of the viewport
    const viewportCenterX = transformContainer.getBoundingClientRect().left + viewportWidth / 2;
    const viewportCenterY = transformContainer.getBoundingClientRect().top + viewportHeight / 2;

    // Calculate how much we need to pan to center the element
    const deltaX = viewportCenterX - elementCenterX;
    const deltaY = viewportCenterY - elementCenterY;

    // Add pulsing animation to the element
    // element.classList.add('panning-to-box');

    // // Remove the animation class after it completes
    // setTimeout(() => {
    //     element.classList.remove('panning-to-box');
    // }, 1500);

    // Apply the pan with animation
    animatePan(panX + deltaX, panY + deltaY);
}

// Function to animate the panning
function animatePan(targetX, targetY) {
    // Get the current pan position
    const startX = panX;
    const startY = panY;

    // Calculate the distance to pan
    const distanceX = targetX - startX;
    const distanceY = targetY - startY;

    // Set up the animation
    const duration = 500; // milliseconds
    const startTime = performance.now();

    // Animation function
    function animate(currentTime) {
        // Calculate the elapsed time
        const elapsedTime = currentTime - startTime;

        // Calculate the progress (0 to 1)
        const progress = Math.min(elapsedTime / duration, 1);

        // Use easeInOutQuad easing function for smooth animation
        const easedProgress = progress < 0.5
            ? 2 * progress * progress
            : 1 - Math.pow(-2 * progress + 2, 2) / 2;

        // Calculate the new pan position
        const newX = startX + distanceX * easedProgress;
        const newY = startY + distanceY * easedProgress;

        // Update the pan position
        setPanPosition(newX, newY);

        // Continue the animation if not finished
        if (progress < 1) {
            requestAnimationFrame(animate);
        }
    }

    // Start the animation
    requestAnimationFrame(animate);
}

// Remove highlight from bounding box
function unhighlightBoundingBox() {
    if (highlightedElement) {
        highlightedElement.classList.remove('highlighted-box');
        highlightedElement = null;
    }
}

// Variable to track the currently highlighted accordion item
let highlightedAccordionItem = null;

// Highlight accordion item when hovering over a bounding box
function highlightAccordionItemFromBox(box) {

    // Remove highlight from all accordion items
    // document.querySelectorAll('.accordion-item').forEach(item => {
    //     item.classList.remove('highlighted-item');
    // });

    // // Get the label(s) of the clicked box
    // const hilightedLabels = JSON.parse(box.dataset.labels || '[]');

    // // Highlight the corresponding accordion item(s)
    // hilightedLabels.forEach(label => {
    //     const accordionItem = document.querySelector(`.accordion-item[data-label="${label}"]`);
    //     if (accordionItem) {
    //         accordionItem.classList.add('highlighted-item');
    //     }
    // });

    // Remove highlight from previous item
    unhighlightAccordionItem();

    // Only proceed if the box is annotated
    if (!box.classList.contains('annotated-box')) {
        return;
    }

    // Get the box coordinates and selector
    const x = parseInt(box.dataset.originalX);
    const y = parseInt(box.dataset.originalY);
    const width = parseInt(box.dataset.originalWidth);
    const height = parseInt(box.dataset.originalHeight);
    const selector = box.dataset.selector;

    // Get the labels from the box
    let labels = [];
    try {
        labels = JSON.parse(box.dataset.labels || '[]');
    } catch (e) {
        console.error('Error parsing labels:', e);
        if (box.dataset.elementType) {
            labels = [box.dataset.elementType];
        }
    }

    // If no labels, return
    if (labels.length === 0) {
        return;
    }

    // For each label, find the corresponding accordion item in both menus
    labels.forEach(label => {
        // Right menu
        highlightAccordionItemInMenu(label, x, y, selector, 'accordion-content-');

        // Left menu
        highlightAccordionItemInMenu(label, x, y, selector, 'left-accordion-content-');
    });
}

// Helper function to highlight accordion items in a specific menu
function highlightAccordionItemInMenu(label, x, y, selector, contentIdPrefix) {
    // Find all accordion items for this label
    const accordionGroup = document.querySelector(`.accordion-group[data-label="${label}"]`);
    if (!accordionGroup) return;

    // Make sure the accordion content is expanded
    const accordionContent = document.getElementById(`${contentIdPrefix}${label}`);
    if (!accordionContent) return;

    if (accordionContent.style.display === 'none') {
        // Expand the accordion
        if (contentIdPrefix === 'accordion-content-') {
            toggleAccordionGroup(label);
        } else {
            toggleLeftAccordionGroup(label);
        }
    }

    // Find the accordion item with matching coordinates
    const items = accordionContent.querySelectorAll('.accordion-item');
    let matchedItem = null;

    items.forEach(item => {
        const itemX = parseInt(item.dataset.x);
        const itemY = parseInt(item.dataset.y);
        const itemSelector = item.dataset.selector;

        // If coordinates and selector match, highlight the item
        if (Math.abs(itemX - x) <= 2 && Math.abs(itemY - y) <= 2 && itemSelector === selector) {
            item.classList.add('highlighted-item');
            highlightedAccordionItem = item;
            matchedItem = item;

            // Only scroll the item into view in the accordion list, don't pan the image
            // This ensures the highlighted item is visible in the list without moving the image
            item.scrollIntoView({ behavior: 'smooth', block: 'nearest' });
        }
    });

    // If a matching item was found, scroll it into view within the accordion container
    if (matchedItem) {
        const accordionContainer = document.getElementById('leftComponentMenu');
        if (accordionContainer) {
            const containerRect = accordionContainer.getBoundingClientRect();
            const itemRect = matchedItem.getBoundingClientRect();

            // Calculate the scroll amount
            const scrollAmount = itemRect.top - containerRect.top - (containerRect.height / 2) + (itemRect.height / 2);

            // Smooth scroll the accordion container
            accordionContainer.scrollBy({
                top: scrollAmount,
                behavior: 'smooth'
            });
        }
    }

}

// Remove highlight from accordion items in both menus
function unhighlightAccordionItem() {
    // Clear the tracked highlighted item
    if (highlightedAccordionItem) {
        highlightedAccordionItem.classList.remove('highlighted-item');
        highlightedAccordionItem = null;
    }

    // Clear all highlighted items in both menus
    document.querySelectorAll('.highlighted-item').forEach(item => {
        item.classList.remove('highlighted-item');
    });
}

// Function to update the appearance of accepted items in the accordion
function updateAcceptedItemAppearance(selector, x, y) {
    // Find the accordion items in both menus that match the coordinates
    document.querySelectorAll('.accordion-item').forEach(item => {
        const itemX = parseInt(item.dataset.x);
        const itemY = parseInt(item.dataset.y);
        const itemSelector = item.dataset.selector;

        // If coordinates and selector match, update the appearance
        if (Math.abs(itemX - x) <= 2 && Math.abs(itemY - y) <= 2 && itemSelector === selector) {
            // Add a class to indicate the item is accepted
            item.classList.add('accepted-item');

            // Update the accept button text if it exists
            const acceptBtn = item.querySelector('.accept-btn');
            if (acceptBtn) {
                acceptBtn.textContent = 'Accepted';
                acceptBtn.classList.add('accepted');
                acceptBtn.disabled = true;
            }
        }
    });
}

// Accept all items with a specific label
async function acceptAllItems(label, event) {
    // Stop event propagation to prevent toggling the accordion
    if (event) {
        event.stopPropagation();
    }

    console.log(`Accepting all items with label: ${label}`);

    try {
        // Find all elements with this label
        const elementsWithLabel = annotatedElementsData.filter(element => {
            // Check if the element has this label
            if (Array.isArray(element.label)) {
                return element.label.includes(label);
            } else {
                return element.label === label;
            }
        });

        if (elementsWithLabel.length === 0) {
            console.warn(`No elements found with label: ${label}`);
            alert(`No elements found with label: ${label}`);
            return;
        }

        // Mark all elements as accepted
        const acceptedElements = elementsWithLabel.map(element => ({
            ...element,
            isAccepted: "Yes"
        }));

        // Update the elements in annotatedElementsData
        acceptedElements.forEach(acceptedElement => {
            const elementKey = createBoundingBoxKey(acceptedElement);
            const index = annotatedElementsData.findIndex(el => createBoundingBoxKey(el) === elementKey);
            if (index !== -1) {
                annotatedElementsData[index] = acceptedElement;
            }
        });

        // Get the filename from the URL
        const urlParams = new URLSearchParams(window.location.search);
        const jsonName = urlParams.get('json') || 'annotation_object.json';

        // Call the append API to update all elements
        console.log(`Calling appendLabels with ${acceptedElements.length} accepted elements`);
        const success = await appendLabels(jsonName, acceptedElements);

        if (success) {
            console.log(`Successfully marked all items with label "${label}" as accepted`);

            // Update the appearance of all accepted items
            acceptedElements.forEach(element => {
                updateAcceptedItemAppearance(element.selector, element.x, element.y);
            });

            // If we're in review mode, update the left accordion menu in real-time
            if (isSubmitted && document.getElementById('leftComponentMenu').style.display === 'flex') {
                // Re-render the left accordion menu to reflect the updated acceptance status
                renderLeftAccordionComponentList();
            }

            // Update the progress bar for review status
            sendLabelDataToParent();

            // Update the Accept All button
            const acceptAllBtns = document.querySelectorAll(`.accordion-header .accept-all-btn`);
            acceptAllBtns.forEach(btn => {
                if (btn.parentElement.parentElement.querySelector('.label-name').textContent === label) {
                    btn.textContent = 'All Accepted';
                    btn.classList.add('accepted');
                    btn.disabled = true;
                }
            });
        } else {
            console.error(`Failed to mark items with label "${label}" as accepted`);
            alert(`Failed to mark items with label "${label}" as accepted. Please try again.`);
        }
    } catch (error) {
        console.error('Error accepting all items:', error);
        alert('Error accepting all items. Check console for details.');
    }
}

// Accept a single item
async function acceptItem(elementId, event) {
    // Stop event propagation to prevent toggling the accordion or panning
    if (event) {
        event.stopPropagation();
    }

    const element = annotatedElementsData[elementId];
    console.log(`Accepting item: ${elementId}`, element);

    if (!element) {
        console.error('Element not found in annotatedElementsData:', elementId);
        return;
    }

    try {
        // Create a copy of the element data with the isAccepted field
        const acceptedElement = {
            ...element,
            isAccepted: "Yes"
        };

        // Update the element in annotatedElementsData
        annotatedElementsData[elementId] = acceptedElement;

        // Get the filename from the URL
        const urlParams = new URLSearchParams(window.location.search);
        const jsonName = urlParams.get('json') || 'annotation_object.json';

        // Prepare data for the API call
        const apiData = [acceptedElement];

        // Call the append API to update the element
        console.log('Calling appendLabels with accepted element:', apiData);
        const success = await appendLabels(jsonName, apiData);

        if (success) {
            console.log('Successfully marked item as accepted');

            // Add a visual indicator to the accepted item in the accordion
            const itemSelector = element.selector;
            const itemX = element.x;
            const itemY = element.y;

            // Find the accordion item in both menus and update its appearance
            updateAcceptedItemAppearance(itemSelector, itemX, itemY);

            // If we're in review mode, update the left accordion menu in real-time
            if (isSubmitted && document.getElementById('leftComponentMenu').style.display === 'flex') {
                // Re-render the left accordion menu to reflect the updated acceptance status
                renderLeftAccordionComponentList();
            }

            // Update the progress bar for review status
            sendLabelDataToParent();
        } else {
            console.error('Failed to mark item as accepted');
            alert('Failed to mark item as accepted. Please try again.');
        }
    } catch (error) {
        console.error('Error accepting item:', error);
        alert('Error accepting item. Check console for details.');
    }
}



// Update the component menu to show the accordion when Submit is clicked
function updateComponentMenuForSubmit() {
    // Set the isSubmitted flag to true
    isSubmitted = true;

    // For the left menu: render the accordion component list
    renderLeftAccordionComponentList();

    // Show the left component menu with accordion view
    // const leftMenu = document.getElementById('leftComponentMenu');
    // if (leftMenu) {
    //     leftMenu.style.display = 'flex';
    // }

    // // For the left menu: update the UI components header
    // const leftHeader = leftMenu.querySelector('.ui-components-header');
    // if (leftHeader) {
    //     leftHeader.textContent = 'Annotated Components';
    // }

    // Keep the right menu as is (same as "inprogress" mode)
    // Make sure the original component list is visible
    // document.getElementById('originalComponentList').style.display = 'block';

    // Make sure the accordion in the right menu is hidden
    // const rightAccordion = document.getElementById('accordionComponentList');
    // if (rightAccordion) {
    //     rightAccordion.style.display = 'none';
    // }

    // Adjust the pan container width to accommodate both menus
    const panContainer = document.getElementById('pan-container');
    // if (panContainer) {
    //     panContainer.style.width = 'calc(100% - 660px)';
    //     panContainer.style.left = '320px';
    // }

    // Update event listeners for all annotated boxes
    // updateBoundingBoxEventListeners();
}

// Function to update event listeners for bounding boxes after submit
function updateBoundingBoxEventListeners() {
    // Get all annotated boxes
    const annotatedBoxes = document.querySelectorAll('.box.annotated-box');

    // Remove existing event listeners and add new ones
    annotatedBoxes.forEach(box => {
        // Clone the box to remove all event listeners
        // const newBox = box.cloneNode(true);
        // box.parentNode.replaceChild(newBox, box);

        // Add click event to highlight the corresponding accordion item
        box.addEventListener('click', () => {
            highlightAccordionItemFromBox(box);

            // Add pulsing animation to the box
            // box.classList.add('panning-to-box');

            // // Remove the animation class after it completes
            // setTimeout(() => {
            //     box.classList.remove('panning-to-box');
            // }, 1500);

            // Pan to the box
            // panToElement(box);
        });
    });
}

// Listen for messages from the parent window
// window.addEventListener('message', (event) => {
//     if (event.data.type === 'SHOW_COMPONENT_MENU') {
//         updateComponentMenuForSubmit();
//     }
// });
