import React, { useState, useEffect } from 'react';

const AccordionComponentList = ({ annotatedElements, onLabelClick, onItemHover, onAcceptAll, onAcceptItem }) => {
  const [groupedElements, setGroupedElements] = useState({});
  const [expandedGroups, setExpandedGroups] = useState({});

  // Group elements by label
  useEffect(() => {
    if (!annotatedElements || annotatedElements.length === 0) {
      setGroupedElements({});
      return;
    }

    const grouped = {};
    
    // Process each annotated element
    annotatedElements.forEach((element, index) => {
      // Handle both array of labels and single label
      const labels = Array.isArray(element.label) ? element.label : [element.label];
      
      // Add element to each of its label groups
      labels.forEach(label => {
        if (!label) return; // Skip empty labels
        
        if (!grouped[label]) {
          grouped[label] = [];
        }
        
        // Add element with its index for reference
        grouped[label].push({
          ...element,
          id: index, // Use index as id for reference
          displayName: `${label} ${grouped[label].length + 1}` // e.g., "Logo 1", "Logo 2"
        });
      });
    });
    
    setGroupedElements(grouped);
    
    // Initialize expanded state for all groups
    const initialExpandedState = {};
    Object.keys(grouped).forEach(label => {
      initialExpandedState[label] = true; // Start with all groups expanded
    });
    setExpandedGroups(initialExpandedState);
  }, [annotatedElements]);

  // Toggle group expansion
  const toggleGroup = (label) => {
    setExpandedGroups(prev => ({
      ...prev,
      [label]: !prev[label]
    }));
  };

  // Filter bounding boxes by label
  const handleLabelClick = (label) => {
    if (onLabelClick) {
      onLabelClick(label);
    }
  };

  // Handle hover on item
  const handleItemHover = (element, isHovering) => {
    if (onItemHover) {
      onItemHover(element, isHovering);
    }
  };

  // Handle accept all for a label
  const handleAcceptAll = (label) => {
    if (onAcceptAll) {
      onAcceptAll(label, groupedElements[label]);
    }
  };

  // Handle accept for a single item
  const handleAcceptItem = (element) => {
    if (onAcceptItem) {
      onAcceptItem(element);
    }
  };

  return (
    <div className="accordion-component-list">
      {Object.keys(groupedElements).length === 0 ? (
        <div className="p-4 text-center text-gray-500">
          No annotated elements found.
        </div>
      ) : (
        Object.entries(groupedElements).map(([label, elements]) => (
          <div key={label} className="accordion-group border-b border-gray-200">
            <div 
              className="accordion-header flex items-center justify-between p-3 cursor-pointer hover:bg-gray-50"
              onClick={() => toggleGroup(label)}
            >
              <div className="flex items-center">
                <span className={`transform transition-transform ${expandedGroups[label] ? 'rotate-90' : ''}`}>
                  <svg width="16" height="16" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                    <path d="M9 18L15 12L9 6" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round"/>
                  </svg>
                </span>
                <span 
                  className="ml-2 font-medium text-gray-700 hover:text-blue-600"
                  onClick={(e) => {
                    e.stopPropagation();
                    handleLabelClick(label);
                  }}
                >
                  {label}
                </span>
              </div>
              <div className="flex items-center">
                <span className="text-sm text-gray-500 mr-3">
                  {elements.length} {elements.length === 1 ? 'item' : 'items'}
                </span>
                <button 
                  className="px-2 py-1 bg-green-500 text-white text-xs rounded hover:bg-green-600"
                  onClick={(e) => {
                    e.stopPropagation();
                    handleAcceptAll(label);
                  }}
                >
                  Accept All
                </button>
              </div>
            </div>
            
            {expandedGroups[label] && (
              <div className="accordion-content bg-gray-50">
                {elements.map((element) => (
                  <div 
                    key={element.id} 
                    className="flex items-center justify-between p-2 pl-8 border-t border-gray-100 hover:bg-gray-100"
                    onMouseEnter={() => handleItemHover(element, true)}
                    onMouseLeave={() => handleItemHover(element, false)}
                  >
                    <span className="text-sm text-gray-600">{element.displayName}</span>
                    <button 
                      className="px-2 py-1 bg-blue-500 text-white text-xs rounded hover:bg-blue-600"
                      onClick={() => handleAcceptItem(element)}
                    >
                      Accept
                    </button>
                  </div>
                ))}
              </div>
            )}
          </div>
        ))
      )}
    </div>
  );
};

export default AccordionComponentList;
