import React, { useState, useEffect, useMemo } from 'react';
import { useNavigate } from 'react-router-dom';
import { useAuth } from '../context/AuthContext';
import { api } from '../config/api';

// Configuration for admin contact numbers who can access review functionality
const ADMIN_CONTACT_NUMBERS = ["1234567890", "9328297704"];

const Dashboard = () => {
  const [tasks, setTasks] = useState([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState(null);
  const [searchQuery, setSearchQuery] = useState('');
  const { user, logout } = useAuth();
  const navigate = useNavigate();

  // Check if current user is an admin (can access review functionality)
  const isAdmin = useMemo(() => {
    return user?.contactNumber && ADMIN_CONTACT_NUMBERS.includes(user.contactNumber);
  }, [user?.contactNumber]);

  // Handle search input changes
  const handleSearchChange = (e) => {
    setSearchQuery(e.target.value);
  };

  // Fetch user tasks when component mounts or user changes
  useEffect(() => {
    const fetchUserTasks = async () => {
      if (!user || !user.contactNumber) {
        console.warn('No user or contact number available, cannot fetch tasks');
        setError('User information is missing. Please try logging in again.');
        setLoading(false);
        return;
      }

      try {
        setLoading(true);
        console.log('Fetching tasks for contact number:', user.contactNumber);
        const response = await api.getUserTasks(user.contactNumber);
        console.log('API response:', response);

        if (response.tasks) {
          console.log('Setting tasks:', response.tasks);
          setTasks(response.tasks);
          // Clear any previous errors
          setError(null);
        } else if (response.error) {
          console.error('API error:', response.error);
          setError(response.error);
        } else {
          console.warn('No tasks or error in response:', response);
          setError('Received an invalid response from the server');
        }
      } catch (err) {
        console.error('Error fetching tasks:', err);
        setError('Failed to load tasks. Please try again later.');
      } finally {
        setLoading(false);
      }
    };

    fetchUserTasks();
  }, [user]);

  const handleLogout = async () => {
    await logout();
    navigate('/login');
  };

  const handleStartAnnotation = async (task) => {
    try {
      // Check if task is in completed state
      const isCompleted = task.status === 'completed' || task.status === 'approved';
      // Check if task is in review state
      const isReview = task.status === 'review';

      if (!isCompleted && !isReview) {
        // Only call create API for non-completed and non-review tasks
        const response = await api.createAnnotation(task.url);
        console.log('Create annotation response:', response);

        // If there's an error, show it but still navigate to the workspace
        if (response.error) {
          console.error('Error creating annotation:', response.error);
        }
      }

      // Navigate to the annotation workspace with the task data and viewOnly flag if completed
      navigate('/annotation-workspace', {
        state: {
          task,
          viewOnly: isCompleted
        }
      });
    } catch (err) {
      console.error('Error starting annotation:', err);
      // Still navigate to the workspace even if there's an error
      const isCompleted = task.status === 'completed' || task.status === 'approved';
      navigate('/annotation-workspace', {
        state: {
          task,
          viewOnly: isCompleted
        }
      });
    }
  };

  // Function to map task status to tab categories
  const getStatusCategory = (status) => {
    switch (status) {
      case 'ready_for_annotation':
        return 'pending';
      case 'inprogress':
        return 'in-progress';
      case 'review':
        return 'review';
      case 'completed':
        return 'completed';
      case 'approved':
        return 'approved';
      case 'returned':
        return 'returned';
      default:
        return 'pending';
    }
  };

  // Filter tasks based on search query only
  const searchFilteredTasks = useMemo(() => {
    if (!tasks || tasks.length === 0) return [];

    return tasks.filter(task => {
      // Filter by search query if one exists
      return searchQuery.trim() === '' ||
        (task.url && task.url.toLowerCase().includes(searchQuery.toLowerCase()));
    });
  }, [tasks, searchQuery]);

  // Get tasks by category
  const inProgressTasks = useMemo(() => {
    return searchFilteredTasks.filter(task => getStatusCategory(task.status) === 'in-progress');
  }, [searchFilteredTasks]);

  const pendingTasks = useMemo(() => {
    return searchFilteredTasks.filter(task => getStatusCategory(task.status) === 'pending');
  }, [searchFilteredTasks]);

  const reviewTasks = useMemo(() => {
    return searchFilteredTasks.filter(task => getStatusCategory(task.status) === 'review');
  }, [searchFilteredTasks]);

  const approvedTasks = useMemo(() => {
    return searchFilteredTasks.filter(task => getStatusCategory(task.status) === 'approved');
  }, [searchFilteredTasks]);

  const submittedTasks = useMemo(() => {
    return searchFilteredTasks.filter(task => getStatusCategory(task.status) === 'completed');
  }, [searchFilteredTasks]);

  return (
    <div className="min-h-screen bg-gray-50">
      <div className="max-w-6xl mx-auto px-4 py-4">
        <div className="flex justify-between items-center mb-6">
          <h1 className="text-xl font-medium">UI Element Labelling Tool</h1>
          <div className="flex items-center space-x-4">
            <div className="flex items-center text-gray-500">
            <svg width="16" height="16" viewBox="0 0 16 16" fill="none" xmlns="http://www.w3.org/2000/svg">
<path d="M3.54424 12.9589C3.94979 12.0035 4.89666 11.3333 6.00004 11.3333H10C11.1034 11.3333 12.0503 12.0035 12.4558 12.9589M10.6667 6.33333C10.6667 7.80609 9.4728 9 8.00004 9C6.52728 9 5.33337 7.80609 5.33337 6.33333C5.33337 4.86057 6.52728 3.66667 8.00004 3.66667C9.4728 3.66667 10.6667 4.86057 10.6667 6.33333ZM14.6667 8C14.6667 11.6819 11.6819 14.6667 8.00004 14.6667C4.31814 14.6667 1.33337 11.6819 1.33337 8C1.33337 4.3181 4.31814 1.33333 8.00004 1.33333C11.6819 1.33333 14.6667 4.3181 14.6667 8Z" stroke="#62748E" strokeWidth="1.33333" strokeLinecap="round" strokeLinejoin="round"/>
</svg>

              <span className="text-sm ml-1">{user?.contactNumber}</span>
            </div>
            <button
              onClick={handleLogout}
              className="flex items-center text-gray-500 hover:text-gray-700"
            >
              <svg width="14" height="14" viewBox="0 0 14 14" fill="none" xmlns="http://www.w3.org/2000/svg">
                <path d="M9.66667 10.3333L13 7M13 7L9.66667 3.66667M13 7H5M7 10.3333C7 10.9533 7 11.2633 6.93185 11.5176C6.74692 12.2078 6.20782 12.7469 5.51764 12.9319C5.26331 13 4.95332 13 4.33333 13H4C3.06812 13 2.60218 13 2.23463 12.8478C1.74458 12.6448 1.35523 12.2554 1.15224 11.7654C1 11.3978 1 10.9319 1 10V4C1 3.06812 1 2.60218 1.15224 2.23463C1.35523 1.74458 1.74458 1.35523 2.23463 1.15224C2.60218 1 3.06812 1 4 1H4.33333C4.95332 1 5.26331 1 5.51764 1.06815C6.20782 1.25308 6.74692 1.79218 6.93185 2.48236C7 2.7367 7 3.04669 7 3.66667" stroke="#62748E" strokeWidth="1.33333" strokeLinecap="round" strokeLinejoin="round"/>
              </svg>

              <span className="text-sm ml-1">Logout</span>
            </button>
          </div>
        </div>

        {/* Error message */}
        {error && (
          <div className="bg-red-100 border border-red-400 text-red-700 px-4 py-3 rounded mb-4">
            {error}
          </div>
        )}

        {/* Search Bar */}
        <div className="mb-8 relative">
          <input
            type="text"
            placeholder="Search by URL or website name..."
            className="w-full h-38px p-3 pl-4 rounded-md bg-white border-none shadow-sm text-gray-500 text-sm"
            value={searchQuery}
            onChange={handleSearchChange}
          />
          {searchQuery && (
            <button
              className="absolute right-2 top-1/2 transform -translate-y-1/2 text-gray-500 hover:text-black-700"
              onClick={() => setSearchQuery('')}
              aria-label="Clear search"
            >
              ✕
            </button>
          )}
        </div>

        {loading ? (
          <div className="flex justify-center py-8">
            <div className="animate-spin rounded-full h-12 w-12 border-t-2 border-b-2 border-blue-500"></div>
          </div>
        ) : (
          <>
            {/* IN PROGRESS Section */}
            <div className="mb-8">
              <h3 className="text-sm font-medium text-blue-500 mb-2">IN PROGRESS</h3>
              {inProgressTasks.length > 0 ? (
                inProgressTasks.map((task, index) => (
                  <div  onClick={() => handleStartAnnotation(task)} key={`in-progress-${index}`} className="bg-white rounded-md shadow-sm mb-2 p-4 cursor-pointer">
                    <div className="flex justify-between items-center">
                      <span className="text-gray-700">{task.url}</span>
                      <div className="flex items-center space-x-4">
                        <div className="flex items-center align-center">
                          <div className="w-2 h-2 rounded-full bg-blue-500 mr-2"></div>
                          <span className="text-sm text-blue-500">In progress</span>
                        </div>
                        <button

                          className="bg-sky-100 text-sky-700 hover:bg-blue-200 text-sm font-medium px-3 py-1 rounded-md flex items-center"
                        >
                          Continue <svg width="12" height="10" viewBox="0 0 12 10" fill="none" xmlns="http://www.w3.org/2000/svg">
<path d="M0.666626 5H11.3333M11.3333 5L7.33329 1M11.3333 5L7.33329 9" stroke="#0069A8" strokeWidth="1.33333" strokeLinecap="round" strokeLinejoin="round"/>
</svg>
                        </button>
                      </div>
                    </div>
                  </div>
                ))
              ) : (
                <div className="text-center py-4 text-gray-500">
                  {searchQuery.trim() !== '' ? (
                    <>No in-progress assignments matching "{searchQuery}" found.</>
                  ) : (
                    <>No in-progress assignments available at this time.</>
                  )}
                </div>
              )}
            </div>

            {/* PENDING Section */}
            <div className="mb-8">
              <h3 className="text-sm font-medium text-orange-500 mb-2">PENDING</h3>
              {pendingTasks.length > 0 ? (
                pendingTasks.map((task, index) => (
                  <div onClick={() => handleStartAnnotation(task)} key={`pending-${index}`} className="bg-white rounded-md shadow-sm mb-2 p-4 cursor-pointer">
                    <div className="flex justify-between items-center">
                      <span className="text-gray-700">{task.url}</span>
                      <div className="flex items-center space-x-4">
                        <div className="flex items-center">
                          <div className="w-2 h-2 rounded-full bg-orange-500 mr-2"></div>
                          <span className="text-sm text-orange-500">Pending</span>
                        </div>
                        <button

                          className="bg-sky-100 text-sky-700 hover:bg-blue-200 text-sm font-medium px-3 py-1 rounded-md flex items-center"
                        >
                          Start <svg width="12" height="10" viewBox="0 0 12 10" fill="none" xmlns="http://www.w3.org/2000/svg">
<path d="M0.666626 5H11.3333M11.3333 5L7.33329 1M11.3333 5L7.33329 9" stroke="#0069A8" strokeWidth="1.33333" strokeLinecap="round" strokeLinejoin="round"/>
</svg>
                        </button>
                      </div>
                    </div>
                  </div>
                ))
              ) : (
                <div className="text-center py-4 text-gray-500">
                  {searchQuery.trim() !== '' ? (
                    <>No pending assignments matching "{searchQuery}" found.</>
                  ) : (
                    <>No pending assignments available at this time.</>
                  )}
                </div>
              )}
            </div>

            {/* REVIEW Section */}
            <div className="mb-8">
              <h3 className="text-sm font-medium text-purple-500 mb-2">REVIEW</h3>
              {reviewTasks.length > 0 ? (
                reviewTasks.map((task, index) => (
                  <div
                    onClick={isAdmin ? () => handleStartAnnotation(task) : undefined}
                    key={`review-${index}`}
                    className={`bg-white rounded-md shadow-sm mb-2 p-4 ${isAdmin ? 'cursor-pointer' : 'cursor-not-allowed opacity-60'}`}
                  >
                    <div className="flex justify-between items-center">
                      <span className="text-gray-700">{task.url}</span>
                      <div className="flex items-center space-x-4">
                        <div className="flex items-center">
                          <div className="w-2 h-2 rounded-full bg-purple-500 mr-2"></div>
                          <span className="text-sm text-purple-500">Review</span>
                        </div>
                        {isAdmin ? (
                          <button
                            className="bg-sky-100 text-sky-700 hover:bg-blue-200 text-sm font-medium px-3 py-1 rounded-md flex items-center"
                          >
                            Review <svg width="12" height="10" viewBox="0 0 12 10" fill="none" xmlns="http://www.w3.org/2000/svg">
<path d="M0.666626 5H11.3333M11.3333 5L7.33329 1M11.3333 5L7.33329 9" stroke="#0069A8" strokeWidth="1.33333" strokeLinecap="round" strokeLinejoin="round"/>
</svg>
                          </button>
                        ) : (
                          <button
                            className="bg-gray-300 text-gray-500 text-sm font-medium px-3 py-1 rounded-md flex items-center cursor-not-allowed"
                            disabled
                            title="Only authorized users can access review functionality"
                          >
                            <svg width="16" height="16" viewBox="0 0 16 16" fill="none" xmlns="http://www.w3.org/2000/svg">
<path d="M4 6V4.5C4 2.567 5.567 1 7.5 1S11 2.567 11 4.5V6M3 6H12C12.552 6 13 6.448 13 7V13C13 13.552 12.552 14 12 14H3C2.448 14 2 13.552 2 13V7C2 6.448 2.448 6 3 6Z" stroke="#9CA3AF" strokeWidth="1.33333" strokeLinecap="round" strokeLinejoin="round"/>
</svg>
                            Restricted
                          </button>
                        )}
                      </div>
                    </div>
                  </div>
                ))
              ) : (
                <div className="text-center py-4 text-gray-500">
                  {searchQuery.trim() !== '' ? (
                    <>No review assignments matching "{searchQuery}" found.</>
                  ) : (
                    <>No review assignments available at this time.</>
                  )}
                </div>
              )}
            </div>

            {/* APPROVED Section */}
            <div className="mb-8">
              <h3 className="text-sm font-medium text-emerald-500 mb-2">APPROVED</h3>
              {approvedTasks.length > 0 ? (
                approvedTasks.map((task, index) => (
                  <div onClick={() => handleStartAnnotation(task)} key={`approved-${index}`} className="bg-white rounded-md shadow-sm mb-2 p-4 cursor-pointer">
                    <div className="flex justify-between items-center">
                      <span className="text-gray-700">{task.url}</span>
                      <div className="flex items-center space-x-4">
                        <div className="flex items-center">
                          <div className="w-2 h-2 rounded-full bg-emerald-500 mr-2"></div>
                          <span className="text-sm text-emerald-500">Approved</span>
                        </div>
                        <button
                          className="bg-slate-400 hover:bg-gray-300 text-white text-sm font-medium px-3 py-1 rounded-md flex items-center"
                        >
                          <svg width="14" height="14" viewBox="0 0 14 14" fill="none" xmlns="http://www.w3.org/2000/svg">
<path d="M13 13L10.1 10.1M11.6667 6.33333C11.6667 9.27885 9.27885 11.6667 6.33333 11.6667C3.38781 11.6667 1 9.27885 1 6.33333C1 3.38781 3.38781 1 6.33333 1C9.27885 1 11.6667 3.38781 11.6667 6.33333Z" stroke="white" strokeWidth="1.33333" strokeLinecap="round" strokeLinejoin="round"/>
</svg>
                          View
                        </button>
                      </div>
                    </div>
                  </div>
                ))
              ) : (
                <div className="text-center py-4 text-gray-500">
                  {searchQuery.trim() !== '' ? (
                    <>No approved assignments matching "{searchQuery}" found.</>
                  ) : (
                    <>No approved assignments available at this time.</>
                  )}
                </div>
              )}
            </div>

            {/* SUBMITTED Section */}
            <div className="mb-8">
              <h3 className="text-sm font-medium text-green-500 mb-2">SUBMITTED</h3>
              {submittedTasks.length > 0 ? (
                submittedTasks.map((task, index) => (
                  <div onClick={() => handleStartAnnotation(task)} key={`submitted-${index}`} className="bg-white rounded-md shadow-sm mb-2 p-4 cursor-pointer">
                    <div className="flex justify-between items-center">
                      <span className="text-gray-700">{task.url}</span>
                      <div className="flex items-center space-x-4">
                        <div className="flex items-center">
                          <div className="w-2 h-2 rounded-full bg-green-500 mr-2"></div>
                          <span className="text-sm text-green-500">Submitted</span>
                        </div>
                        <button

                          className="bg-slate-400 hover:bg-gray-300 text-white text-sm font-medium px-3 py-1 rounded-md flex items-center"
                        >
                          <svg width="14" height="14" viewBox="0 0 14 14" fill="none" xmlns="http://www.w3.org/2000/svg">
<path d="M13 13L10.1 10.1M11.6667 6.33333C11.6667 9.27885 9.27885 11.6667 6.33333 11.6667C3.38781 11.6667 1 9.27885 1 6.33333C1 3.38781 3.38781 1 6.33333 1C9.27885 1 11.6667 3.38781 11.6667 6.33333Z" stroke="white" strokeWidth="1.33333" strokeLinecap="round" strokeLinejoin="round"/>
</svg>
                          View
                        </button>
                      </div>
                    </div>
                  </div>
                ))
              ) : (
                <div className="text-center py-4 text-gray-500">
                  {searchQuery.trim() !== '' ? (
                    <>No submitted assignments matching "{searchQuery}" found.</>
                  ) : (
                    <>No submitted assignments available at this time.</>
                  )}
                </div>
              )}
            </div>
          </>
        )}
      </div>
    </div>
  );
};

export default Dashboard;
