@import "tailwindcss";

@layer base {
  html {
    font-family: 'Figtree', sans-serif;
  }
}

body {
  line-height: 1.6;
  color: #333;
  background-color: #f5f5f5;
  margin: 0;
  min-width: 320px;
  min-height: 100vh;
}


/* Typography */
h1 {
  font-size: 2rem;
  font-weight: bold;
  margin-bottom: 1rem;
}

h2 {
  font-size: 1.5rem;
  font-weight: bold;
  margin-bottom: 0.75rem;
}



/* Header */
.header {
  background-color: #ffffff;
  padding: 1rem;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
  border-bottom: 1px solid #e0e0e0;
}

/* Navigation */
.nav {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

/* Buttons */
.btn {
  display: inline-block;
  padding: 0.5rem 1rem;
  background-color: #4a90e2;
  color: white;
  border: none;
  border-radius: 4px;
  cursor: pointer;
  text-decoration: none;
  font-size: 1rem;
  transition: all 0.2s ease;
}

.btn:hover {
  background-color: #3a80d2;
  transform: translateY(-1px);
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.btn:disabled {
  background-color: #a0a0a0;
  cursor: not-allowed;
  transform: none;
  box-shadow: none;
}

.btn-secondary {
  background-color: #f8f8f8;
  color: #333;
  border: 1px solid #ddd;
}

.btn-secondary:hover {
  background-color: #e8e8e8;
  transform: translateY(-1px);
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.btn-secondary:disabled {
  background-color: #f0f0f0;
  color: #999;
  cursor: not-allowed;
  transform: none;
  box-shadow: none;
}

/* Cards */
.card {
  background-color: white;
  border-radius: 8px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.05);
  padding: 1rem;
  margin-bottom: 1rem;
  border: 1px solid #eaeaea;
}

/* Forms */
input, select, textarea {
  width: 100%;
  padding: 0.75rem;
  margin-bottom: 1rem;
  border: 1px solid #ddd;
  border-radius: 6px;
  transition: border-color 0.2s ease;
  font-size: 0.9rem;
}

input:focus, select:focus, textarea:focus {
  outline: none;
  border-color: #4a90e2;
  box-shadow: 0 0 0 2px rgba(74, 144, 226, 0.2);
}

/* Grid */
.grid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(250px, 1fr));
  gap: 1rem;
}

/* Utilities */
.text-center {
  text-align: center;
}

.mt-1 {
  margin-top: 0.25rem;
}

.mt-2 {
  margin-top: 0.5rem;
}

.mt-4 {
  margin-top: 1rem;
}

.mb-1 {
  margin-bottom: 0.25rem;
}

.mb-2 {
  margin-bottom: 0.5rem;
}

.mb-4 {
  margin-bottom: 1rem;
}

.p-1 {
  padding: 0.25rem;
}

.p-2 {
  padding: 0.5rem;
}

.p-4 {
  padding: 1rem;
}

.flex {
  display: flex;
}

.flex-1 {
  flex: 1;
}

.flex-col {
  flex-direction: column;
}

.items-center {
  align-items: center;
}

.justify-between {
  justify-content: space-between;
}

.justify-end {
  justify-content: flex-end;
}

.space-x-2 > * + * {
  margin-left: 0.5rem;
}

.space-y-2 > * + * {
  margin-top: 0.5rem;
}

.w-full {
  width: 100%;
}

.h-full {
  height: 100%;
}

.rounded {
  border-radius: 6px;
}

.shadow {
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.shadow-md {
  box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
}

.bg-white {
  background-color: white;
}

.bg-gray {
  background-color: #f0f0f0;
}

.text-primary {
  color: #4a90e2;
}

.text-secondary {
  color: #666;
}

.border {
  border: 1px solid #ddd;
}

.border-l {
  border-left: 1px solid #ddd;
}

.overflow-auto {
  overflow: auto;
}

.overflow-hidden {
  overflow: hidden;
}

.overflow-y-auto {
  overflow-y: auto;
}

.relative {
  position: relative;
}

.absolute {
  position: absolute;
}

.bottom-4 {
  bottom: 1rem;
}

.left-4 {
  left: 1rem;
}

.right-4 {
  right: 1rem;
}

/* Enhanced button styles */
/* .save-btn {
  display: inline-block;
  padding: 0.5rem 1.25rem;
  background-color: white;
  color: #333;
  border: 1px solid #ddd;
  border-radius: 4px;
  cursor: pointer;
  font-weight: 500;
  transition: all 0.2s ease;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
}

.save-btn:hover {
  background-color: #f8f8f8;
  transform: translateY(-1px);
  box-shadow: 0 2px 5px rgba(0, 0, 0, 0.15);
}

.save-btn:active {
  background-color: #f0f0f0;
  transform: translateY(0);
  box-shadow: inset 0 1px 2px rgba(0, 0, 0, 0.1);
}

.submit-btn {
  display: inline-block;
  padding: 0.5rem 1.25rem;
  background-color: #4a90e2;
  color: white;
  border: none;
  border-radius: 4px;
  cursor: pointer;
  font-weight: 500;
  transition: all 0.2s ease;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.2);
}

.submit-btn:hover {
  background-color: #3a80d2;
  transform: translateY(-1px);
  box-shadow: 0 3px 6px rgba(0, 0, 0, 0.2);
}

.submit-btn:active {
  background-color: #2a70c2;
  transform: translateY(0);
  box-shadow: inset 0 1px 2px rgba(0, 0, 0, 0.2);
} */

/* Verification modal buttons */
/* .return-btn {
  display: inline-block;
  padding: 0.75rem 1.5rem;
  background-color: #f8f9fa;
  color: #495057;
  border: 1px solid #ced4da;
  border-radius: 4px;
  cursor: pointer;
  font-weight: 500;
  transition: all 0.2s ease;
  box-shadow: 0 1px 2px rgba(0, 0, 0, 0.1);
}

.return-btn:hover {
  background-color: #e9ecef;
  transform: translateY(-1px);
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.15);
}

.confirm-btn {
  display: inline-block;
  padding: 0.75rem 1.5rem;
  background-color: #4a90e2;
  color: white;
  border: none;
  border-radius: 4px;
  cursor: pointer;
  font-weight: 500;
  transition: all 0.2s ease;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.2);
} */

/* Progress bar styles */
.progress-container {
  display: flex;
  align-items: center;
  width: 300px;
}

.progress-bar-wrapper {
  width: 100%;
  height: 1.5rem;
  background-color: #e0e0e0;
  border-radius: 999px;
  position: relative;
  overflow: hidden;
  box-shadow: inset 0 1px 3px rgba(0, 0, 0, 0.1);
}

.progress-bar {
  height: 100%;
  background-color: #4caf50;
  border-radius: 999px;
  transition: width 0.5s ease-out;
  background-image: linear-gradient(45deg,
    rgba(255,255,255,.15) 25%,
    transparent 25%,
    transparent 50%,
    rgba(255,255,255,.15) 50%,
    rgba(255,255,255,.15) 75%,
    transparent 75%,
    transparent);
  background-size: 1rem 1rem;
  animation: progress-animation 1s linear infinite;
  box-shadow: 0 1px 2px rgba(0, 0, 0, 0.1);
}

.progress-label {
  position: absolute;
  inset: 0;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 0.75rem;
  font-weight: 600;
  color: white;
}

@keyframes progress-animation {
  0% {
    background-position: 1rem 0;
  }
  100% {
    background-position: 0 0;
  }
}

.mr-3 {
  margin-right: 0.75rem;
}

.mr-4 {
  margin-right: 1rem;
}

/* Verification modal styles */
.fixed {
  position: fixed;
}

.inset-0 {
  top: 0;
  right: 0;
  bottom: 0;
  left: 0;
}

.z-50 {
  z-index: 50;
}

.bg-opacity-50 {
  --bg-opacity: 0.5;
}

.bg-black {
  background-color: #000;
}

.max-w-2xl {
  max-width: 42rem;
}

.max-h-\[90vh\] {
  max-height: 90vh;
}

.rounded-t-lg {
  border-top-left-radius: 0.5rem;
  border-top-right-radius: 0.5rem;
}

.rounded-lg {
  border-radius: 0.5rem;
}

.bg-gray-100 {
  background-color: #f3f4f6;
}

.bg-gray-50 {
  background-color: #f9fafb;
}

.bg-yellow-50 {
  background-color: #fffbeb;
}

.border-gray-200 {
  border-color: #e5e7eb;
}

.border-yellow-200 {
  border-color: #fef3c7;
}

.text-gray-800 {
  color: #1f2937;
}

.text-gray-700 {
  color: #374151;
}

.text-green-600 {
  color: #059669;
}

.text-orange-500 {
  color: #f97316;
}

.text-yellow-800 {
  color: #92400e;
}

.text-gray-500 {
  color: #6b7280;
}

.text-gray-400 {
  color: #9ca3af;
}

.space-x-3 > * + * {
  margin-left: 0.75rem;
}

.divide-y > * + * {
  border-top-width: 1px;
  border-top-style: solid;
}

.divide-gray-200 > * + * {
  border-color: #e5e7eb;
}
