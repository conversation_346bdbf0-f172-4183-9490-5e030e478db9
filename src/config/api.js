// Get API domains from environment variables
export const API_BASE_URL = import.meta.env.VITE_API_DOMAIN;
export const SOCKET_DOMAIN = import.meta.env.VITE_SOCKET_DOMAIN;
export const TASKS_API_URL = import.meta.env.VITE_TASKS_API_DOMAIN;

// Get the auth token from localStorage
const getAuthToken = () => {
    return localStorage.getItem('auth_token');
};

// Add auth token to request headers
const getAuthHeaders = () => {
    const token = getAuthToken();
    const headers = {
        'Content-Type': 'application/json',
        'Accept': 'application/json'
    };

    if (token) {
        headers['Authorization'] = `Bearer ${token}`;
    }

    return headers;
};

// Helper function to handle API responses
const handleResponse = async (response) => {
    // Log response details for debugging
    console.log('API Response:', {
        url: response.url,
        status: response.status,
        statusText: response.statusText,
        headers: Object.fromEntries([...response.headers.entries()])
    });

    // For non-2xx responses, convert to error
    if (!response.ok) {
        // Try to get error message from response
        try {
            const errorData = await response.json();
            console.error('API error data:', errorData);
            throw new Error(errorData.error || `API error: ${response.status}`);
        } catch (e) {
            // If can't parse JSON, use status text
            console.error('Failed to parse error response:', e);
            throw new Error(`API error: ${response.status} ${response.statusText}`);
        }
    }

    // Parse and log the successful response
    try {
        const data = await response.json();
        console.log('API response data:', data);
        return data;
    } catch (e) {
        console.error('Failed to parse success response:', e);
        throw new Error('Invalid JSON response from server');
    }
};

export const api = {
    // Element and label operations
    getElements: () => fetch(`${API_BASE_URL}/elements`)
        .then(handleResponse)
        .catch(error => ({ success: false, error: error.message })),

    getLabels: () => fetch(`${API_BASE_URL}/get-labels`)
        .then(handleResponse)
        .catch(error => ({ success: false, error: error.message })),

    saveLabels: (labels) => fetch(`${API_BASE_URL}/save-labels`, {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json',
        },
        body: JSON.stringify(labels),
        credentials: 'include'
    })
    .then(handleResponse)
    .catch(error => ({ success: false, error: error.message })),

    createAnnotation: (filename) => {
        console.log('Making API call to create annotation');
        console.log('With payload:', { filename });

        return fetch(`${API_BASE_URL}/proxy/create`, {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
                'Accept': 'application/json'
            },
            credentials: 'include',
            body: JSON.stringify({ filename })
        })
        .then(response => {
            console.log('Create API response status:', response.status);
            return handleResponse(response);
        })
        .catch(error => {
            console.error('Create API call error:', error);
            return { success: false, error: error.message };
        });
    },

    // Authentication operations
    register: (contactNumber, password) => fetch(`${API_BASE_URL}/register`, {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json',
        },
        body: JSON.stringify({ contactNumber, password }),
        credentials: 'include'
    })
    .then(handleResponse)
    .catch(error => ({ success: false, error: error.message })),

    login: (contactNumber, password) => fetch(`${API_BASE_URL}/login`, {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json',
            'Accept': 'application/json'
        },
        body: JSON.stringify({ contactNumber, password })
    })
    .then(response => handleResponse(response))
    .then(data => {
        // Store the auth token if login was successful
        if (data.success && data.auth_token) {
            localStorage.setItem('auth_token', data.auth_token);
        }
        return data;
    })
    .catch(error => ({ success: false, error: error.message })),

    logout: () => {
        // Get the auth token
        const token = getAuthToken();

        return fetch(`${API_BASE_URL}/logout`, {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
                'Accept': 'application/json',
                'Authorization': token ? `Bearer ${token}` : ''
            }
        })
        .then(handleResponse)
        .then(data => {
            // Clear the auth token on successful logout
            localStorage.removeItem('auth_token');
            return data;
        })
        .catch(error => ({ success: false, error: error.message }));
    },

    getCurrentUser: () => {
        // Get the auth token
        const token = getAuthToken();

        // If no token, return early with auth_required flag
        if (!token) {
            console.log('No auth token found, user is not authenticated');
            return Promise.resolve({
                success: false,
                error: 'Not authenticated',
                auth_required: true
            });
        }

        return fetch(`${API_BASE_URL}/user`, {
            method: 'GET',
            headers: {
                'Content-Type': 'application/json',
                'Accept': 'application/json',
                'Authorization': `Bearer ${token}`
            }
        })
        .then(response => {
            console.log('Get user API response status:', response.status);
            return handleResponse(response);
        })
        .catch(error => {
            console.error('Get user API error:', error);
            return { success: false, error: error.message };
        });
    },

    // Get user tasks from the tasks API via proxy
    getUserTasks: (contactNumber) => {
        console.log('Making API call to proxy endpoint for user tasks');
        console.log('With payload:', { contact_number: contactNumber });

        // Get the auth token
        const token = getAuthToken();

        return fetch(`${API_BASE_URL}/proxy/get-user-tasks`, {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
                'Accept': 'application/json',
                'Authorization': token ? `Bearer ${token}` : ''
            },
            body: JSON.stringify({ contact_number: contactNumber })
        })
        .then(response => {
            console.log('Raw API response status:', response.status);
            return handleResponse(response);
        })
        .catch(error => {
            console.error('API call error:', error);
            return { success: false, error: error.message };
        });
    },

    // Get bounding boxes from the API
    getBoundingBoxes: (jsonName) => {
        console.log('Making API call to get bounding boxes');
        console.log('For JSON file:', jsonName);

        // Get the auth token
        const token = getAuthToken();

        return fetch(`${API_BASE_URL}/proxy/get-bounding-boxes?json_name=${encodeURIComponent(jsonName)}`, {
            method: 'GET',
            headers: {
                'Accept': 'application/json',
                'Content-Type': 'application/json',
                'Authorization': token ? `Bearer ${token}` : ''
            }
        })
        .then(response => {
            console.log('Get bounding boxes API response status:', response.status);
            return handleResponse(response);
        })
        .catch(error => {
            console.error('Get bounding boxes API error:', error);
            return { success: false, error: error.message };
        });
    },

    // Submit task for review
    submitForReview: (filename) => {
        console.log('Making API call to submit for review');
        console.log('With payload:', { filename });

        // Get the auth token
        const token = getAuthToken();

        return fetch(`${API_BASE_URL}/proxy/review`, {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
                'Accept': 'application/json',
                'Authorization': token ? `Bearer ${token}` : ''
            },
            body: JSON.stringify({ filename })
        })
        .then(response => {
            console.log('Review API response status:', response.status);
            return handleResponse(response);
        })
        .catch(error => {
            console.error('Review API call error:', error);
            return { success: false, error: error.message };
        });
    },

    // Submit final task after review
    submitFinal: (filename) => {
        console.log('Making API call to submit final task');
        console.log('With payload:', { filename });

        // Get the auth token
        const token = getAuthToken();

        return fetch(`${API_BASE_URL}/proxy/submit`, {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
                'Accept': 'application/json',
                'Authorization': token ? `Bearer ${token}` : ''
            },
            body: JSON.stringify({ filename })
        })
        .then(response => {
            console.log('Submit final API response status:', response.status);
            return handleResponse(response);
        })
        .catch(error => {
            console.error('Submit final API call error:', error);
            return { success: false, error: error.message };
        });
    }
};