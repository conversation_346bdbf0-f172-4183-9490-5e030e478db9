import { useState } from 'react'
import { BrowserRouter as Router, Routes, Route } from 'react-router-dom'

// Pages
import Dashboard from './pages/Dashboard'
import AnnotationWorkspace from './pages/AnnotationWorkspace'
import Login from './pages/Login'

// Components
import ProtectedRoute from './components/ProtectedRoute'

// Context
import { AuthProvider } from './context/AuthContext'

function App() {
  const [showHelp, setShowHelp] = useState(false)

  return (
    <Router>
      <AuthProvider>
        <div className="relative overflow-hidden">
          {/* Help Button */}
          <button
            className="fixed bottom-4 right-4 bg-blue-600 text-white rounded-full w-12 h-12 flex items-center justify-center shadow-lg z-10"
            onClick={() => setShowHelp(true)}
          >
            <span className="text-xl">?</span>
          </button>

          {/* Home Component Modal */}
          {showHelp && <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center p-4 z-50">
            <div className="bg-white rounded-lg shadow-xl max-w-6xl w-full max-h-[90vh] overflow-hidden flex flex-col">
              <div className="bg-gray-100 p-4 rounded-t-lg border-b border-gray-200 flex justify-between items-center">
                <h2 className="text-xl font-bold text-gray-800">DAS Training Guide</h2>
                <button
                  className="text-gray-500 hover:text-gray-700"
                  onClick={() => setShowHelp(false)}
                >
                  <svg xmlns="http://www.w3.org/2000/svg" className="h-6 w-6" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
                  </svg>
                </button>
              </div>
              <div className="flex-1 overflow-auto">
                <iframe
                  src="https://das-training-guide-tawny.vercel.app/"
                  title="DAS Training Guide"
                  className="w-full h-full border-none"
                  style={{ minHeight: '70vh' }}
                />
              </div>
              <div className="bg-gray-50 p-4 border-t border-gray-200 flex justify-end">
                <button
                  className="px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700"
                  onClick={() => setShowHelp(false)}
                >
                  Close
                </button>
              </div>
            </div>
          </div>}

          {/* Routes */}
          <Routes>
            <Route path="/login" element={<Login />} />
            <Route path="/" element={
              <ProtectedRoute>
                <Dashboard />
              </ProtectedRoute>
            } />
            <Route path="/annotation-workspace" element={
              <ProtectedRoute>
                <AnnotationWorkspace />
              </ProtectedRoute>
            } />
          </Routes>
        </div>
      </AuthProvider>
    </Router>
  )
}

export default App
